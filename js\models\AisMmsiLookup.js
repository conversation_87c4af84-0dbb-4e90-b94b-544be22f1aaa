const mongoose = require('mongoose');
const db = require('../modules/db');

const aisMmsiLookupSchema = new mongoose.Schema({
    mmsi: { type: String, required: true },
    onboard_vessel_id: { type: mongoose.Schema.Types.ObjectId, required: true },
    last_message_id: { type: mongoose.Schema.Types.ObjectId, required: true },
    last_message_timestamp: { type: Date, required: true },
    collection: { type: String, required: true },
    db: { type: String, required: true },
    data: { type: Object, required: true }
});

aisMmsiLookupSchema.index({ mmsi: 1, onboard_vessel_id: 1 }, { unique: true })

const AisMmsiLookup = db.lookups.model('AisMmsiLookup', aisMmsiLookupSchema, 'ais_mmsi_lookup');

module.exports = AisMmsiLookup