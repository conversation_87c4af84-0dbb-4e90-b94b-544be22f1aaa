require('dotenv').config()
const db = require("../../modules/db");
const { getTerminalConfirmation, getTerminalInput } = require('../../utils/functions');

const getCollectionNames = async () => {
    return (await db.qmAis.listCollections()).map(c => c.name).filter(c => !c.startsWith('system.'))
}

const getMonthRange = (month) => {
    const [year, monthNum] = month.split('-').map(Number);
    const startDate = new Date(Date.UTC(year, monthNum - 1, 1, 0, 0, 0, 0));
    const endDate = new Date(Date.UTC(year, monthNum, 0, 23, 59, 59, 999));
    return [startDate, endDate];
}

const updateAis = (ais) => {
    return ais.map(a => {
        const { unitId, metadata, onboard_vessel_id, mmsi, ...rest } = a;

        return {
            ...rest,
            metadata: {
                onboard_vessel_id,
                unit_id: unitId,
                mmsi
            },
            details: metadata || {}
        };
    });
};

async function run() {
    const month = await getTerminalInput('Enter month (YYYY-MM): ')
    const dateRange = getMonthRange(month)
    console.log('Date Range', dateRange)
    console.log('Using Source DB', db.qmAis.name)
    const collectionNames = await getCollectionNames()
    console.log('collectionNames.length', collectionNames.length)

    if (!(await db.aisRaw.listCollections({ name: month })).find(c => c.name === month)) {
        console.error(`Raw collection ${month} does not exist in DB ${db.aisRaw.name}. You need to create it first before running this script`)
        process.exit(1)
    }

    const confirmation = await getTerminalConfirmation(`Deleting and inserting data in collection ${month} in db ${db.aisRaw.name}, continue? (y/n): `)
    if (!confirmation) {
        console.log('User did not confirm, exiting...');
        process.exit(0);
    }

    const rawCollection = db.aisRaw.collection(month)

    console.log('deleting data...')
    await rawCollection.deleteMany({})
    console.log('fetching ais...')

    let progress = 0
    for (const cName of collectionNames) {
        progress++;
        console.log('fetching ais for collection', cName)

        const unitId = cName.split('_ais')[0]
        const collection = db.qmAis.collection(cName)
        const ais = (await collection.find({
            timestamp: {
                $gte: dateRange[0],
                $lte: dateRange[1]
            }
        }).toArray()).map(c => {
            return {
                ...c,
                unitId
            }
        })

        console.log('ais.length', ais.length, 'for collection', cName)

        if (ais.length === 0) {
            console.log('no ais found for collection', cName)
            continue
        }

        const updatedAis = updateAis(ais)

        console.log('inserting data for collection', cName)
        await rawCollection.insertMany(updatedAis)

        console.log('inserted data for collection', cName)

        console.log('progress', progress / collectionNames.length * 100, '%')
    }

    console.log('Success')
    process.exit(0)
}

Promise.all([
    new Promise((resolve, reject) => {
        db.qmAis.once('open', resolve);
        db.qmAis.on('error', reject);
    }),
    new Promise((resolve, reject) => {
        db.aisRaw.once('open', resolve);
        db.aisRaw.on('error', reject);
    }),
]).then(() => {
    setTimeout(() => {
        run()
    }, 1000);
}).catch(err => {
    console.error('Failed to establish database connections:', err);
    process.exit(1);
});