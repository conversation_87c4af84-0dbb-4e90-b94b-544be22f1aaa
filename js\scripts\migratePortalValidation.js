require('dotenv').config();
const db = require('../modules/db');
const AisMmsiLookup = require('../models/AisMmsiLookup');
const { getAisCollectionNames, getTerminalConfirmation, calculateDistanceInMeters } = require('../utils/functions');

const BATCH_SIZE = 1000;
const PORTAL_DISTANCE_THRESHOLD = 100;

async function isValidateDistance({ hostLat, hostLon, vesselLat, vesselLon, thresholdKm }) {
    try {
        if (!hostLat || !hostLon || !vesselLat || !vesselLon ||
            isNaN(hostLat) || isNaN(hostLon) || isNaN(vesselLat) || isNaN(vesselLon)) {
            return false;
        }

        const distanceMeters = await calculateDistanceInMeters(hostLat, hostLon, vesselLat, vesselLon);
        const distanceKm = distanceMeters / 1000;

        return distanceKm <= thresholdKm;
    } catch (error) {
        console.error('Error calculating portal distance validation:', error);
        return false;
    }
}

const updateRecordPortalValid = (record, portalValid) => ({
    ...record,
    metadata: {
        ...record.metadata,
        message: { ...record.metadata.message, portal_valid: portalValid }
    }
});

const logProgress = (current, total, label = 'Progress') => {
    if (current % 100 === 0 || current === total) {
        const progress = Math.round((current / total) * 100);
        console.log(`   📈 ${label}: ${current}/${total} (${progress}%)`);
    }
};

const processBatch = async (collection, items, operation) => {
    for (let i = 0; i < items.length; i += BATCH_SIZE) {
        const batch = items.slice(i, i + BATCH_SIZE);
        await operation(batch);
        logProgress(i + batch.length, items.length);
    }
};

async function updateAisRecords() {
    console.log('🚀 Updating AIS records...');
    const collections = await getAisCollectionNames(db.qmAis);
    let totalUpdated = 0;

    for (const collectionName of collections) {
        console.log(`\n🔄 Processing: ${collectionName}`);
        const collection = db.qmAis.collection(collectionName);
        
        const records = await collection.find({
            $or: [
                { 'metadata.message.portal_valid': { $exists: false } },
                { 'metadata.message.portal_valid': null }
            ]
        }).toArray();

        if (records.length === 0) {
            console.log(`   ✅ No records to update`);
            continue;
        }

        console.log(`   📋 Processing ${records.length} records...`);
        const updatedRecords = [];
        const recordIds = [];
        let validCount = 0;

        for (let i = 0; i < records.length; i++) {
            const record = records[i];
            let portalValid = false;

            try {
                const { nav_latitude: vLat, nav_longitude: vLon, host_location_latitude: hLat, host_location_longitude: hLon } = record.metadata?.message || {};
                
                if (hLat && hLon && vLat && vLon) {
                    portalValid = await isValidateDistance({ hostLat: hLat, hostLon: hLon, vesselLat: vLat, vesselLon: vLon, thresholdKm: PORTAL_DISTANCE_THRESHOLD });
                }
            } catch (error) {
                console.error(`   ⚠️  Error processing ${record._id}: ${error.message}`);
            }

            updatedRecords.push(updateRecordPortalValid(record, portalValid));
            recordIds.push(record._id);
            if (portalValid) validCount++;
            
            logProgress(i + 1, records.length, 'Processing');
        }

        // Replace records
        console.log(`   🗑️  Deleting ${recordIds.length} records...`);
        await processBatch(collection, recordIds, batch => collection.deleteMany({ _id: { $in: batch } }));
        
        console.log(`   📥 Inserting ${updatedRecords.length} records...`);
        await processBatch(collection, updatedRecords, batch => collection.insertMany(batch, { ordered: false }));

        console.log(`   ✅ ${collectionName}: ${updatedRecords.length} updated (${validCount} valid, ${updatedRecords.length - validCount} invalid)`);
        totalUpdated += updatedRecords.length;
    }

    return totalUpdated;
}

async function cleanLookups() {
    console.log('\n📋 Cleaning lookups...');
    const totalLookups = await AisMmsiLookup.countDocuments({});
    
    if (totalLookups === 0) {
        console.log('   ✅ No lookups to process');
        return 0;
    }

    console.log(`   📊 Processing ${totalLookups} lookups...`);
    const lookups = await AisMmsiLookup.find({});
    const lookupsByCollection = lookups.reduce((acc, lookup) => {
        (acc[lookup.collection] = acc[lookup.collection] || []).push(lookup);
        return acc;
    }, {});

    const mmsiToRemove = [];
    
    for (const [collectionName, collectionLookups] of Object.entries(lookupsByCollection)) {
        try {
            console.log(`   🔍 Checking ${collectionLookups.length} MMSIs in ${collectionName}...`);
            const collection = db.qmAis.collection(collectionName);
            const mmsis = collectionLookups.map(l => l.mmsi);

            const invalidMmsis = await collection.aggregate([
                { $match: { mmsi: { $in: mmsis }, 'metadata.message.portal_valid': false } },
                { $sort: { mmsi: 1, timestamp: -1 } },
                { $group: { _id: '$mmsi' } },
                { $project: { mmsi: '$_id', _id: 0 } }
            ]).toArray();

            mmsiToRemove.push(...invalidMmsis.map(r => r.mmsi));
            console.log(`   ✅ Found ${invalidMmsis.length} invalid MMSIs in ${collectionName}`);
        } catch (error) {
            console.error(`   ⚠️  Error processing ${collectionName}: ${error.message}`);
        }
    }

    console.log(`   📊 Analysis: ${mmsiToRemove.length}/${totalLookups} to remove`);
    
    if (mmsiToRemove.length === 0) {
        console.log('   ✅ No lookups to remove');
        return 0;
    }

    const confirmation = await getTerminalConfirmation(
        `\n❓ Remove ${mmsiToRemove.length} invalid lookups? (y/n): `
    );
    
    if (!confirmation) {
        console.log('   ⏸️  Cleanup skipped');
        return 0;
    }

    console.log('   🗑️  Removing lookups...');
    let removedCount = 0;
    await processBatch(null, mmsiToRemove, async (batch) => {
        const result = await AisMmsiLookup.deleteMany({ mmsi: { $in: batch } });
        removedCount += result.deletedCount;
    });

    console.log(`   ✅ Removed ${removedCount} lookups`);
    return removedCount;
}

async function dryRun() {
    console.log('🔍 DRY RUN Analysis\n');
    const collections = await getAisCollectionNames(db.qmAis);
    let totalToUpdate = 0;
    let totalWithoutCoords = 0;

    for (const name of collections) {
        const collection = db.qmAis.collection(name);
        const toUpdate = await collection.countDocuments({ $or: [{ 'metadata.message.portal_valid': { $exists: false } }, { 'metadata.message.portal_valid': null }] });
        const withoutCoords = await collection.countDocuments({ $or: [{ 'metadata.message.host_location_latitude': { $exists: false } }, { 'metadata.message.host_location_longitude': { $exists: false } }] });
        
        console.log(`   📁 ${name}: ${toUpdate} to update, ${withoutCoords} without coords`);
        totalToUpdate += toUpdate;
        totalWithoutCoords += withoutCoords;
    }

    const totalLookups = await AisMmsiLookup.countDocuments({});
    console.log(`\n📈 Summary: ${totalToUpdate} AIS updates, ${totalWithoutCoords} missing coords, ${totalLookups} lookups`);
}

async function main() {
    const startTime = Date.now();
    const isDryRun = process.argv.includes('--dry-run');
    
    // Setup DB connections
    await Promise.all([
        new Promise((resolve, reject) => { db.qmAis.once('open', resolve); db.qmAis.on('error', reject); }),
        new Promise((resolve, reject) => { db.lookups.once('open', resolve); db.lookups.on('error', reject); })
    ]);
    console.log('🔗 Connected to databases');

    if (isDryRun) {
        await dryRun();
        process.exit(0);
    }

    const confirmation = await getTerminalConfirmation(
        `\n❓ Migrate portal validation in ${process.env.NODE_ENV}? (y/n): `
    );
    
    if (!confirmation) {
        console.log('⏸️  Cancelled');
        process.exit(0);
    }

    try {
        const aisUpdated = await updateAisRecords();
        const lookupsRemoved = await cleanLookups();
        
        const timeElapsed = (Date.now() - startTime) / 1000;
        console.log(`\n🎉 Migration complete!`);
        console.log(`📊 AIS updated: ${aisUpdated}, Lookups removed: ${lookupsRemoved}`);
        console.log(`⏱️  Time: ${timeElapsed.toFixed(2)}s`);
    } catch (error) {
        console.error('💥 Migration failed:', error);
        process.exit(1);
    }
    
    process.exit(0);
}

// Error handling
process.on('unhandledRejection', (err) => { console.error('💥 Unhandled rejection:', err); process.exit(1); });
process.on('uncaughtException', (err) => { console.error('💥 Uncaught exception:', err); process.exit(1); });

main().catch(err => { console.error('💥 Script failed:', err); process.exit(1); });
