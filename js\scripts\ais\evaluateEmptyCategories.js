require('dotenv').config();
const db = require("../../modules/db");
const path = require('path');
const fs = require('fs');

const generateAisTelemetry = (aisMessagesSorted) => {
    const aisTelemetry = {}
    aisMessagesSorted.forEach(ais => {
        if (!aisTelemetry[ais.metadata.mmsi]) {
            aisTelemetry[ais.metadata.mmsi] = []
        }
        aisTelemetry[ais.metadata.mmsi].push(ais)
    })
    return aisTelemetry
}


async function run() {
    const allCollections = (await db.aisRaw.db.listCollections().toArray()).filter(c => !c.name.startsWith('system.')).map(c => c.name).sort((a, b) => a.localeCompare(b))
    console.log('allCollections', allCollections)

    const emptyAis = (await Promise.all(allCollections.map(async (collectionName) => {
        return (await db.aisRaw.collection(collectionName).find({
            "details.message.design_ais_ship_type_name": ''
        }, {
            projection: {
                _id: 1,
                "metadata.mmsi": 1,
                "metadata.unit_id": 1,
                "details.message.design_ais_ship_type_name": 1,
                timestamp: 1
            }
        }).toArray()).map(doc => {
            return {
                ...doc,
                lookup: {
                    db: db.aisRaw.name,
                    collection: collectionName
                }
            }
        })
    }))).flat()

    console.log('emptyAis', emptyAis.length)

    const uniqueEmptyMmsi = [...new Set(emptyAis.map(ais => ais.metadata.mmsi))]

    console.log('uniqueEmptyMmsi', uniqueEmptyMmsi.length)

    const nonEmptyAis = (await Promise.all(allCollections.map(async (collectionName) => {
        return (await db.aisRaw.collection(collectionName).find({
            "metadata.mmsi": { $in: uniqueEmptyMmsi },
            "details.message.design_ais_ship_type_name": { $ne: '' }
        }, {
            projection: {
                _id: 1,
                "metadata.mmsi": 1,
                "metadata.unit_id": 1,
                "details.message.design_ais_ship_type_name": 1,
                timestamp: 1
            }
        }).toArray()).map(doc => {
            return {
                ...doc,
                lookup: {
                    db: db.aisRaw.name,
                    collection: collectionName
                }
            }
        })
    }))).flat()

    console.log('nonEmptyAis', nonEmptyAis.length)

    const uniqueNonEmptyMmsi = [...new Set(nonEmptyAis.map(ais => ais.metadata.mmsi))]

    console.log('uniqueNonEmptyMmsi', uniqueNonEmptyMmsi.length)

    const vesselsReportingCategory = uniqueNonEmptyMmsi;
    const vesselsNotReportingCategory = uniqueEmptyMmsi.filter(mmsi => !vesselsReportingCategory.includes(mmsi))

    console.log('vesselsReportingCategory', vesselsReportingCategory.length)
    console.log('vesselsNotReportingCategory', vesselsNotReportingCategory.length)

    // below logic is expensive. optimized via generateAisTelemetry()
    // const aisTelemetry = uniqueEmptyMmsi.map(mmsi => {
    //     const telemetry = emptyAis.filter(ais => ais.metadata.mmsi === mmsi).concat(nonEmptyAis.filter(ais => ais.metadata.mmsi === mmsi)).sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
    //     return {
    //         mmsi,
    //         telemetry
    //     }
    // })

    // strip telemtry mmsi to 100 vessels because the data is too large to analyze
    const telemetryFilter = vesselsReportingCategory.slice(0, 50).concat(vesselsNotReportingCategory.slice(0, 50))
    const telemetryAisMessagesSorted = emptyAis.filter(ais => telemetryFilter.includes(ais.metadata.mmsi)).concat(nonEmptyAis.filter(ais => telemetryFilter.includes(ais.metadata.mmsi))).sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())

    const aisTelemetry = generateAisTelemetry(telemetryAisMessagesSorted);

    console.log('aisTelemetry', Object.keys(aisTelemetry).length)
    // console.log('aisTelemetry', aisTelemetry)

    const results = {
        description: 'This JSON contains some evaluation stats for AIS vessels that have reported at least one empty category in their all-time messages. "vesselsReportingCategory" are the vessels which have reported at least one empty and at least one non-empty category. "vesselsNotReportingCategory" are the vessels which have reported at least one empty category and has never reported a non-empty category. Additionally, "aisTelemetry" contains the all-time telemetry for first 100 vessels (which have reported at least one empty category) sorted by timestamp. The telemetry data is intentionally stripped to 100 vessels because the data is too large to analyze.',
        vesselsReportingCategory: {
            count: vesselsReportingCategory.length,
            mmsi: vesselsReportingCategory
        },
        vesselsNotReportingCategory: {
            count: vesselsNotReportingCategory.length,
            mmsi: vesselsNotReportingCategory
        },
        aisTelemetry
    }

    const filePath = path.join(__dirname, '../temp/ais-empty-category-evaluation.json')
    fs.writeFileSync(filePath, JSON.stringify(results))
    console.log(`AIS category evaluation saved to ${filePath}`)
}

Promise.all([
    new Promise((resolve, reject) => {
        db.aisRaw.once('open', resolve);
    })
]).then(() => {
    setTimeout(async () => {
        await run();
        console.log('Success')
        process.exit(0)
    }, 1000);
}).catch(err => {
    console.error('Failed to establish database connections:', err);
    process.exit(1);
});