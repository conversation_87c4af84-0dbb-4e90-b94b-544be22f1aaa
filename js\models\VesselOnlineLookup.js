const mongoose = require('mongoose');
const db = require('../modules/db');

const vesselOnlineLookupSchema = new mongoose.Schema({
	vessel_id: { type: mongoose.Schema.Types.ObjectId, required: true, unique: true, index: true },
	last_online_at: { type: Date, required: false },
	updated_at: { type: Date, required: true, default: () => new Date().toISOString() }
}, { minimize: false });

vesselOnlineLookupSchema.index({ updated_at: -1 });

const VesselOnlineLookup = db.lookups.model('VesselOnlineLookup', vesselOnlineLookupSchema, 'vessel_online_lookup');

module.exports = VesselOnlineLookup;


