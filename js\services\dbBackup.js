/**
 * This script requires installation of MongoDB Command Line Database Tools
 * https://www.mongodb.com/try/download/database-tools
 */

const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const archiver = require('archiver');
const cron = require('node-cron');
const { s3 } = require('../modules/awsS3');
const os = require('os');
const { postLogToSlack } = require('../modules/notifyLog');

// Resetting pathname to remove the trailing database name if exists
const url = new URL(process.env.MONGO_URI);
url.pathname = '/'
const CONNECTION_URL = url.toString();
const DB_CONFIGS = [
    {
        name: 'quartermaster',
        collections: 'all'
    },
    {
        name: 'quartermaster-dev',
        collections: 'all'
    },
    {
        name: 'quartermaster-shared',
        collections: 'all'
    },
    {
        name: 'artifact_processor',
        collections: 'all'
    },
    {
        name: 'audio_processor',
        collections: 'all'
    },
    {
        name: 'ais_raw',
        collections: getCurrentAndLastMonth()
    },
    {
        name: 'lookups',
        collections: 'all'
    },
    {
        name: 'locations_raw',
        collections: getCurrentAndLastMonth()
    },
    {
        name: 'locations_optimized',
        collections: getCurrentAndLastMonth()
    }
];
const S3_BUCKET = 'quartermaster-web-application-backups';

// Schedule backup to run daily at midnight
cron.schedule('0 1 * * *', () => {
    console.log('DB backup cron job initialized');
    backupMongoDB();
});

async function backupMongoDB() {
    console.log('Initiating backups for databases', DB_CONFIGS.map(c => c.name).join(', '));
    const ts = new Date().getTime();
    const timestamp = new Date().toISOString().replace(/[:.-]/g, '_');
    const tempDir = path.join(os.tmpdir(), `backup_${timestamp}`);

    if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
    }

    try {
        const successBackups = [];
        for (const db of DB_CONFIGS) {
            let command;
            if (db.collections === 'all') {
                command = `mongodump --uri "${CONNECTION_URL}" --db ${db.name} --gzip --out "${tempDir}/${db.name}"`;
            } else {
                command = db.collections.map(collectionName => `mongodump --uri "${CONNECTION_URL}" --db ${db.name} --collection ${collectionName} --gzip --out "${tempDir}"`).join(' && ');
            }
            if (!command) throw new Error(`Unable to build command for ${db.name}`);
            console.log(`Executing backup for ${db.name}...`);
            await new Promise((resolve, reject) => {
                exec(command, (error) => {
                    if (error) {
                        reject(error);
                        return;
                    }
                    console.log(`Backup for ${db.name} completed successfully! Files saved in: ${tempDir}`);
                    resolve();
                });
            });

            // Create zip file
            const zipPath = path.join(tempDir, `${db.name}.zip`);
            await zipBackup(`${tempDir}/${db.name}`, zipPath);

            // Upload to S3
            const s3Key = `backups/${timestamp}/${db.name}.zip`;
            const meta = await uploadToS3(zipPath, s3Key);

            successBackups.push({
                db: db.name,
                s3Key,
                size: meta.ContentLength
            });

            // Clean up the backup files
            fs.rmSync(`${tempDir}/${db.name}`, { recursive: true, force: true });
            fs.rmSync(zipPath, { force: true });
        }

        // Clean up temp directory after all backups are done
        fs.rmSync(tempDir, { recursive: true, force: true });
        console.log('All backups completed and uploaded to S3');

        postLogToSlack({
            severity: 'info',
            message: `Successfully backed up ${successBackups.map(d => `${d.db} (${formatBytes(d.size)})`).join(', ')} databases\nTime taken: ${new Date().getTime() - ts} ms`,
            stack: 'N/A'
        });
    } catch (error) {
        console.error('Error during backup process:', error);
        postLogToSlack({
            severity: 'fatal',
            message: 'Error during database backup',
            stack: error.stack
        });
        // Clean up temp directory in case of error
        if (fs.existsSync(tempDir)) {
            fs.rmSync(tempDir, { recursive: true, force: true });
        }
    }
}

function zipBackup(dir, outputPath) {
    return new Promise((resolve, reject) => {
        const output = fs.createWriteStream(outputPath);
        const archive = archiver('zip', {
            zlib: { level: 9 }, // Set compression level
        });

        output.on('close', () => {
            console.log(`Zipped backup created at: ${outputPath}. Total size: ${archive.pointer() / 1048576} MB`);
            resolve();
        });

        archive.on('error', (err) => {
            reject(err);
        });

        archive.pipe(output);
        archive.directory(dir + '/', false);
        archive.finalize();
    });
}

async function uploadToS3(filePath, key) {
    try {
        const fileStream = fs.createReadStream(filePath);
        const params = {
            Bucket: S3_BUCKET,
            Key: key,
            Body: fileStream
        };

        await s3.upload(params).promise();
        const meta = await s3.headObject({ Bucket: S3_BUCKET, Key: key }).promise();
        console.log(`Successfully uploaded backup to S3: ${key}`);
        return meta;
    } catch (error) {
        console.error(`Error uploading to S3: ${error}`);
        throw error;
    }
}

// returns [YYYY-MM, YYYY-MM-1]
function getCurrentAndLastMonth() {
    const now = new Date();
    const currentMonth = now.toISOString().split('T')[0].split('-').slice(0, 2).join('-');
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1).toISOString().split('T')[0].split('-').slice(0, 2).join('-');
    return [currentMonth, lastMonth];
}

function formatBytes(bytes) {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    const index = Math.floor(Math.log(bytes) / Math.log(1024));
    return (bytes / Math.pow(1024, index)).toFixed(2) + ' ' + units[index];
}