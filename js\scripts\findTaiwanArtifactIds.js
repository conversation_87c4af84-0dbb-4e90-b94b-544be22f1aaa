require('dotenv').config();
const { default: mongoose } = require("mongoose");
const db = require("../modules/db");
const fs = require('fs');

const TAIWAN_REGION_GROUP_ID = '681c253f9f43051a7748b2c1'

async function findTaiwanArtifactIds() {
    const taiwanVessels = await db.qmShared.collection('vessels').find({
        region_group_id: new mongoose.Types.ObjectId(TAIWAN_REGION_GROUP_ID)
    }).toArray()
    console.log('taiwanVessels.length', taiwanVessels.length)

    const taiwanVesselObjectIds = taiwanVessels.map(vessel => vessel._id)
    const allTaiwanUnitIds = [...new Set(taiwanVessels.map(vessel => vessel.unit_id).concat(taiwanVessels.flatMap(vessel => vessel.units_history.map(unit => unit.unit_id))))]

    // console.log(taiwanVesselObjectIds)
    console.log('allTaiwanUnitIds.length', allTaiwanUnitIds.length)

    // const nonTaiwanVesselsLinkedToTaiwanUnits = await db.qmShared.collection('vessels').find({
    //     units_history: {
    //         $elemMatch: { unit_id: { $in: Array.from(allTaiwanUnitIds) } }
    //     },
    //     region_group_id: { $ne: new mongoose.Types.ObjectId(TAIWAN_REGION_GROUP_ID) }
    // }).toArray()
    // console.log('nonTaiwanVesselsLinkedToTaiwanUnits.length', nonTaiwanVesselsLinkedToTaiwanUnits.length)

    const artifacts = await db.qmai.collection('analysis_results').find({
        $or: [
            { unit_id: { $in: allTaiwanUnitIds } },
            { onboard_vessel_id: { $in: taiwanVesselObjectIds } }
        ]
    }, { projection: { _id: 1 } }).toArray()

    console.log('artifacts.length', artifacts.length)

    const tawiwanArtifactsData = {
        unit_ids: allTaiwanUnitIds,
        artifact_ids: artifacts.map(artifact => artifact._id.toString())
    }

    const filePath = __dirname + '/temp/taiwan_artifacts_data.json'
    fs.writeFileSync(filePath, JSON.stringify(tawiwanArtifactsData, null, 2))

    console.log('taiwanArtifactsData written to file in dir', filePath)
}

Promise.all([
    new Promise((resolve, reject) => {
        db.qmShared.once('open', resolve);
        db.qmShared.on('error', reject);
    }),
    new Promise((resolve, reject) => {
        db.qmai.once('open', resolve);
        db.qmai.on('error', reject);
    }),
]).then(() => {
    findTaiwanArtifactIds()
}).catch(err => {
    console.error('Failed to establish database connections:', err);
    process.exit(1);
});