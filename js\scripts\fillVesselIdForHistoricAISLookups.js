
require('dotenv').config()

const db = require('../modules/db');
const { getTerminalConfirmation } = require('../utils/functions');


async function run() {

    const collection = db.lookups.collection('ais_mmsi_lookup')

    console.log('Executing script on db', db.lookups.name, 'collection', collection.name)

    const aisLookups = await collection.find({ onboard_vessel_id: { $exists: false } }).toArray()
    const aisWithVesselId = await collection.find({ onboard_vessel_id: { $exists: true } }).toArray()

    console.log('aisLookups', aisLookups.length)
    console.log('aisWithVesselId', aisWithVesselId.length)


    const collectionsNames = [...new Set(aisLookups.map(aisLookup => aisLookup.collection))].reduce((acc, key) => {
        acc[key] = [];
        return acc;
    }, {});

    aisLookups.forEach(aisLookup => {
        collectionsNames[aisLookup.collection].push(aisLookup.last_message_id)
    })

    console.log('collectionsNames length', Object.keys(collectionsNames).length)

    const refRecords = []
    for (const collectionName of Object.keys(collectionsNames)) {
        const records = await db.qmAis.collection(collectionName).find({ _id: { $in: collectionsNames[collectionName] } }).toArray()
        refRecords.push(...records)
        // const refCollection = aisLookup.collection;
        // const refId = aisLookup.last_message_id;
        // const refRecord = await db.qmAis.collection(refCollection).findOne({ _id: refId })
        // aisLookup.onboard_vessel_id = refRecord.onboard_vessel_id
        // updates.push({ _id: aisLookup._id, onboard_vessel_id: refRecord.onboard_vessel_id })
    }

    console.log('refRecords', refRecords.length)

    const missingRefs = aisLookups.filter(aisLookup => !refRecords.find(refRecord => refRecord._id.toString() === aisLookup.last_message_id.toString()))
    console.log('missingRefs length', missingRefs.length)

    const updates = []
    const idsToRemove = []
    aisLookups.forEach(aisLookup => {
        const refRecord = refRecords.find(refRecord => refRecord._id.toString() === aisLookup.last_message_id.toString())
        if (!refRecord) {
            idsToRemove.push(aisLookup._id)
            return;
        }
        aisLookup.onboard_vessel_id = refRecord.onboard_vessel_id
        if (aisWithVesselId.find(ais => ais.mmsi === aisLookup.mmsi && ais.onboard_vessel_id.toString() === aisLookup.onboard_vessel_id.toString())) {
            idsToRemove.push(aisLookup._id)
        } else {
            updates.push({ updateOne: { filter: { _id: aisLookup._id }, update: { $set: { onboard_vessel_id: refRecord.onboard_vessel_id } } } })
        }
    })

    console.log('updates length', updates.length)
    console.log('idsToRemove length', idsToRemove.length)

    const confirmation = await getTerminalConfirmation(`Updating ${updates.length} records in database ${db.lookups.name} collection ${collection.name}, continue? (y/n): `);

    if (!confirmation) {
        console.log('User did not confirm, exiting...');
        process.exit(0);
    }

    await collection.deleteMany({ _id: { $in: idsToRemove } })
    await collection.bulkWrite(updates)


    return;
}

Promise.all([
    new Promise((resolve, reject) => {
        db.lookups.once('open', resolve);
        db.lookups.on('error', reject);
    }),
    new Promise((resolve, reject) => {
        db.qmAis.once('open', resolve);
        db.qmAis.on('error', reject);
    })
]).then(async () => {
    await run();
    console.log('Success')
    process.exit(0)
})