require('dotenv').config();
const db = require("../../modules/db");
const { getTerminalConfirmation } = require('../../utils/functions');

async function run() {
    const collection = db.aisRaw.collection('2025-10')

    const countryDocs = await collection.find({ 'details.message.portal_country': { $exists: true } }).toArray()

    console.log('countryDocs', countryDocs.length)

    const updatedCountryDocs = countryDocs.map(doc => {
        const country = doc.details.message.portal_country
        delete doc.details.message.portal_country;
        doc.details.message.portal_registry_country = country
        return doc
    })

    console.log('updatedCountryDocs', updatedCountryDocs.length)

    const confirmation = await getTerminalConfirmation(`Clearing and inserting ${updatedCountryDocs.length} records in environment ${process.env.NODE_ENV} in collection ${db.aisRaw.name}/${collection.name}, continue? (y/n): `)
    if (!confirmation) {
        console.log('User did not confirm, exiting...');
        process.exit(0);
    }

    console.log('deleting records')
    const res = await collection.deleteMany({ _id: { $in: updatedCountryDocs.map(doc => doc._id) } })
    console.log('deleted records', res.deletedCount)

    console.log('inserting records')
    const res2 = await collection.insertMany(updatedCountryDocs)
    console.log('inserted records', res2.insertedCount)

    console.log('Success')
    process.exit(0)
}

Promise.all([
    new Promise((resolve, reject) => {
        db.aisRaw.once('open', resolve);
        db.aisRaw.on('error', reject);
    }),
]).then(() => {
    setTimeout(async () => {
        await run()
        console.log('Success')
        process.exit(0)
    }, 1000);
}).catch(err => {
    console.error('Failed to establish database connections:', err);
    process.exit(1);
});