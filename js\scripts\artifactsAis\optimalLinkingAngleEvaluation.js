require('dotenv').config();
const db = require("../../modules/db");
const { getTerminalConfirmation, getBearingRange } = require('../../utils/functions');

const startDate = new Date('2025-09-01T00:00:00.000Z')
const endDate = new Date('2025-09-30T23:59:59.999Z')

var baseQuery = {
    timestamp: { $gte: startDate, $lte: endDate },
    location: { $ne: null },
    vessel_presence: true,
    super_category: { $ne: null }
}

const superCategoryMap = {
    Tankers: ["Tanker hazard cat D", "Tanker", "Tanker (no additional information)", "Tanker carrying dangerous goods", "Tanker hazard cat B", "Tanker hazard cat C"],
    Passenger: ["Passenger ship", "Passenger ship (no additional information)"],
    Cargo: ["Cargo ship (no additional information)", "Cargo ship hazard cat B", "Cargo ship", "Cargo ship hazard cat D", "Cargo ship carrying dangerous goods", "Cargo ship hazard cat C"],
    Military: ["Law enforcement", "Engaged in military operations"],
    Fishing: ["Fishing"],
    Tugs: ["Tug", "Towing", "Towing exceeds 200m or wider than 25m"],
    "Pleasure Craft": ["High speed craft", "Pleasure"],
    Tanker: [],
    "Special Craft": ["RR Resolution No.18", "Pilot vessel", "Engaged in dredging or underwater operations", "SAR"],
}

function computeAisProximityConfidence({
    artifactBearing,
    aisBearing,
    bearingThreshold,
    artifactTimestamp,
    aisTimestamp,
    timeThreshold
}) {
    // --- Bearing score ---
    const diff = Math.abs(artifactBearing - aisBearing) % 360;
    const bDiff = diff > 180 ? 360 - diff : diff;
    let bearingScore = 1 - Math.min(bDiff, bearingThreshold) / bearingThreshold;

    // --- Time score ---
    const tDiff = Math.abs(artifactTimestamp - aisTimestamp);
    let timeScore = 1 - Math.min(tDiff, timeThreshold) / timeThreshold;

    // --- Combine scores (weighted average) ---
    const weightBearing = 0.5;
    const weightTime = 0.5;

    const confidence = bearingScore * weightBearing + timeScore * weightTime;

    return confidence;
}

const AIS_TIME_WINDOW_MS = 300 * 1000;
// const AIS_BEARING_THRESHOLD_DEG = 10;

async function getAisInfo(artifact, bearingThreshold) {
    if (!artifact.onboard_vessel_id) {
        // console.log(`[getAisInfo] (${artifact._id}) No vessel ID for artifact`);
        return null;
    }

    if (!artifact.true_bearing) {
        // console.log(`[getAisInfo] (${artifact._id}) No true bearing for artifact`);
        return null;
    }

    const timestampISO = new Date(artifact.timestamp).toISOString()
    const isoSplit = timestampISO.split("-");
    const yearMonth = isoSplit[0] + "-" + isoSplit[1];

    const timeRange = [
        new Date(new Date(artifact.timestamp).getTime() - AIS_TIME_WINDOW_MS),
        new Date(new Date(artifact.timestamp).getTime() + AIS_TIME_WINDOW_MS)
    ]
    const bearingRange = getBearingRange(artifact.true_bearing, bearingThreshold);

    // console.log(`[getAisInfo] (${artifact._id}) timeRange`, artifact.timestamp, '=>', timeRange)
    // console.log(`[getAisInfo] (${artifact._id}) bearingRange`, artifact.true_bearing, '=>', bearingRange)

    const query = {
        "metadata.onboard_vessel_id": artifact.onboard_vessel_id,
        timestamp: {
            $gte: timeRange[0],
            $lte: timeRange[1]
        },
        "details.message.portal_true_bearing_deg": {
            $gte: bearingRange[0],
            $lte: bearingRange[1]
        }
    }

    var aisInfos = await db.aisRaw.collection(yearMonth).find(query).toArray();

    if (!aisInfos.length) {
        // console.log(`[getAisInfo] (${artifact._id}) No AIS info found`);
        return null;
    }

    const hasNonEmptyCategory = aisInfos.some(aisInfo => aisInfo.details.message.design_ais_ship_type_name !== '');

    if (hasNonEmptyCategory) {
        // filter for only non-empty categories
        aisInfos = aisInfos.filter(aisInfo => aisInfo.details.message.design_ais_ship_type_name !== '');
    }

    const aisInfosWithConfidence = aisInfos.map(aisInfo => ({
        aisInfo,
        confidence: computeAisProximityConfidence({
            artifactBearing: artifact.true_bearing,
            aisBearing: aisInfo.details.message.portal_true_bearing_deg,
            bearingThreshold,
            artifactTimestamp: artifact.timestamp,
            aisTimestamp: aisInfo.timestamp,
            timeThreshold: AIS_TIME_WINDOW_MS
        })
    }));

    const aisInfoWithHighestConfidence = aisInfosWithConfidence.sort((a, b) => b.confidence - a.confidence)[0];

    // console.log(`[getAisInfo] (${artifact._id}) aisInfo`, aisInfoWithHighestConfidence.aisInfo)
    // console.log(`[getAisInfo] (${artifact._id}) confidence`, aisInfoWithHighestConfidence.confidence)

    return {
        aisInfo: {
            _id: aisInfoWithHighestConfidence.aisInfo._id,
            ...aisInfoWithHighestConfidence.aisInfo.details.message
        },
        confidence: aisInfoWithHighestConfidence.confidence
    };
}

const getVesselFirstAisMessage = async (vesselId) => {
    const collections = ['2025-08', '2025-09', '2025-10']
    for (const collection of collections) {
        const message = await db.aisRaw.collection(collection).findOne({
            'metadata.onboard_vessel_id': vesselId
        })
        if (message) {
            return message;
        }
    }
    return null
}

const getSDROnlyVessels = async () => {
    const vessels = await db.qmShared.collection('vessels').find().toArray()

    const sdrOnlyVessels = (await Promise.all(vessels.map(async (vessel) => {
        const firstMessage = await getVesselFirstAisMessage(vessel._id)
        if (firstMessage) {
            vessel.sdr_mount_timestamp = firstMessage.timestamp
        }
        return vessel;
    }))).filter(vessel => vessel.sdr_mount_timestamp)

    return sdrOnlyVessels
}

const getSDROnlyArtifacts = async () => {
    const sdrOnlyVessels = await getSDROnlyVessels()
    // fs.writeFileSync(path.join(__dirname, '../temp/sdr_only_vessels.json'), JSON.stringify(sdrOnlyVessels, null, 2))
    const sdrVesselMountMap = sdrOnlyVessels.reduce((acc, vessel) => {
        acc[vessel._id] = vessel.sdr_mount_timestamp
        return acc
    }, {})

    baseQuery.onboard_vessel_id = { $in: sdrOnlyVessels.map(vessel => vessel._id) }

    const allArtifacts = await db.qmai.collection('analysis_results').find(baseQuery, {
        projection: {
            _id: 1,
            super_category: 1,
            timestamp: 1,
            onboard_vessel_id: 1,
            true_bearing: 1
        }
    }).toArray()
    // const artifacts = await db.qmai.collection('analysis_results').find(aisOnlyQuery).toArray()

    const sdrMountFilter = (artifact) => {
        if (!sdrVesselMountMap[artifact.onboard_vessel_id]) {
            return false
        }
        return new Date(artifact.timestamp).getTime() >= new Date(sdrVesselMountMap[artifact.onboard_vessel_id]).getTime()
    }

    const allArtifactsFiltered = allArtifacts.filter(sdrMountFilter)
    // const artifactsFiltered = artifacts.filter(sdrMountFilter)



    return allArtifactsFiltered;
}

function calculatePercentileStats(artifacts) {
    const percentile = 70;
    // Extract numeric confidence scores
    const confidences = artifacts
        .map(artifact => artifact.portal.ais_info.proximity_confidence)
        .filter(v => typeof v === 'number' && !isNaN(v))
        .sort((a, b) => a - b)

    if (confidences.length === 0) {
        return { percentileValue: 0, countAbove: 0 }
    }

    const N = confidences.length
    const rank = (percentile / 100) * (N + 1)
    const lowerIndex = Math.floor(rank) - 1
    const upperIndex = Math.ceil(rank) - 1
    const fraction = rank - Math.floor(rank)

    let percentileValue
    if (upperIndex >= N) {
        percentileValue = confidences[N - 1]
    } else if (lowerIndex < 0) {
        percentileValue = confidences[0]
    } else {
        percentileValue =
            confidences[lowerIndex] +
            fraction * (confidences[upperIndex] - confidences[lowerIndex])
    }

    const countAbove = confidences.filter(v => v >= percentileValue).length
    const countPercentage = countAbove / confidences.length * 100

    return { percentileValue, countAbove, countPercentage }
}

async function run() {

    const allArtifacts = await getSDROnlyArtifacts()
    console.log('allArtifacts', allArtifacts.length)

    if (allArtifacts.length === 0) {
        console.log('No artifacts found for range', startDate, endDate)
    }

    const bearingEvaluations = []
    const bearingThresholds = [10, 9, 8, 7, 6, 5, 4, 3, 2, 1];
    for (const bearingThreshold of bearingThresholds) {
        // await Promise.all(
        //     [10, 5].map(async (bearingThreshold) => {
        const artifactsWithAisInfo = (await Promise.all(allArtifacts.map(async (artifact) => {
            const aisInfo = await getAisInfo(artifact, bearingThreshold)
            if (!artifact.portal) {
                artifact.portal = {}
            }
            artifact.portal.ais_info = aisInfo ? { data: aisInfo.aisInfo, proximity_confidence: aisInfo.confidence } : null
            return artifact;
        }))).flat()

        console.log('artifactsWithAisInfo', artifactsWithAisInfo[0])

        // for (const artifact of artifacts) {
        //     const aisInfo = await getAisInfo(artifact)
        //     updateQueries.push({
        //         updateOne: {
        //             filter: { _id: artifact._id },
        //             update: { $set: { 'portal.ais_info': aisInfo } }
        //         }
        //     })
        // }

        const linkedArtifacts = artifactsWithAisInfo.filter(artifact => artifact.portal.ais_info)

        console.log('linkedArtifacts', linkedArtifacts.length)

        const matchedArtifacts = linkedArtifacts.filter(artifact => {
            const cat = artifact.super_category
            return superCategoryMap[cat].includes(artifact.portal.ais_info.data.design_ais_ship_type_name)
        })

        console.log('matchedArtifacts', matchedArtifacts.length)

        const mismatchedArtifactsAis = linkedArtifacts.filter(artifact => {
            const cat = artifact.super_category
            return !superCategoryMap[cat].includes(artifact.portal.ais_info.data.design_ais_ship_type_name)
        })

        console.log('mismatchedArtifactsAis', mismatchedArtifactsAis.length)

        const percentileStats = calculatePercentileStats(linkedArtifacts)
        console.log('percentileStats', percentileStats)

        bearingEvaluations.push({
            bearingThreshold,
            totalArtifacts: allArtifacts.length,
            linkedArtifacts: linkedArtifacts.length,
            matchedArtifacts: matchedArtifacts.length,
            mismatchedArtifactsAis: mismatchedArtifactsAis.length,
            percentileStats
        })
        // }))
    }

    console.log('bearingEvaluations', bearingEvaluations)
}

Promise.all([
    new Promise((resolve, reject) => {
        db.aisRaw.once('open', resolve);
    }),
    new Promise((resolve, reject) => {
        db.qmai.once('open', resolve);
    }),
    new Promise((resolve, reject) => {
        db.qmShared.once('open', resolve);
    })
]).then(async () => {
    await run();
    console.log('Success')
    process.exit(0)
}).catch(err => {
    console.error('Failed to establish database connections:', err);
    process.exit(1);
});