const db = require("../modules/db");
const seaVisionApi = require("../modules/seaVisionApi");
const { fetchArtifactImageBase64, fetchArtifactImageMetadata } = require("../utils/functions");
const { ObjectId } = require("mongodb");

const submitRFDetection = async (artifacts) => {
    try {

        const artifactsDataProd = []
        const artifactsDataStag = []

        artifacts.forEach(artifact => {
            const objProd = {
                source: "RF",
                trackingCode: artifact._id.toString(),
                lat: artifact.location.coordinates[1],
                lon: artifact.location.coordinates[0],
                time: new Date(artifact.timestamp).toISOString(),
                mmsi: artifact === undefined || artifact === null || Number.isNaN(Number(artifact.imo_number)) ? undefined : Number(artifact.imo_number),
                attributes: {
                    det_conf: artifact.det_conf,
                    det_nbbox: artifact.det_nbbox,
                    det_nbbox_area: artifact.det_nbbox_area,
                    category: artifact.category,
                    super_category: artifact.super_category,
                    text_extraction: artifact.text_extraction,
                    color: artifact.color,
                    size: artifact.size,
                    weapons: artifact.weapons,
                    country_flag: artifact.country_flag,
                    others: artifact.others,
                    captures: {
                        bucket_name: artifact.bucket_name,
                        aws_region: artifact.aws_region,
                        image_path: artifact.image_path,
                        video_path: artifact.video_path
                    },
                    true_bearing: artifact.true_bearing,
                    host_vessel: artifact.host_vessel,
                    vessel_orientation: artifact.vessel_orientation,
                    home_country: artifact.home_country,
                    platform: artifact.unit_id,
                    onboard_vessel_id: artifact.onboard_vessel_id
                }
            }
            const objStag = {
                source: "RF",
                trackingCode: artifact._id.toString(),
                lat: artifact.location.coordinates[1],
                lon: artifact.location.coordinates[0],
                time: new Date(artifact.timestamp).toISOString(),
                mmsi: artifact === undefined || artifact === null || Number.isNaN(Number(artifact.imo_number)) ? undefined : Number(artifact.imo_number),
                attributes: {
                    det_conf: artifact.det_conf,
                    det_nbbox: artifact.det_nbbox,
                    det_nbbox_area: artifact.det_nbbox_area,
                    category: artifact.category,
                    super_category: artifact.super_category,
                    text_extraction: artifact.text_extraction,
                    color: artifact.color,
                    size: artifact.size,
                    weapons: artifact.weapons,
                    country_flag: artifact.country_flag,
                    others: artifact.others,
                    captures: {
                        bucket_name: artifact.bucket_name,
                        aws_region: artifact.aws_region,
                        image_path: artifact.image_path,
                        video_path: artifact.video_path
                    },
                    true_bearing: artifact.true_bearing,
                    host_vessel: artifact.host_vessel,
                    vessel_orientation: artifact.vessel_orientation,
                    home_country: artifact.home_country,
                    platform: artifact.unit_id,
                    onboard_vessel_id: artifact.onboard_vessel_id
                }
            }

            artifactsDataProd.push(objProd)
            artifactsDataStag.push(objStag)
        })

        const prodRes = await seaVisionApi.PostRFDetection(artifactsDataProd, true, false)
        const stagingRes = await seaVisionApi.PostRFDetection(artifactsDataStag, true, true)

        return { prodRes, stagingRes }
    } catch (err) {
        throw err;
    }
}

const submitCameraDetection = async (artifact) => {
    try {
        const file = await fetchArtifactImageBase64(artifact);
        // console.log('[submitCameraDetection] file is', file.slice(0, 100))

        const metadata = await fetchArtifactImageMetadata(artifact);
        // console.log('[submitCameraDetection] metadata is', metadata)

        const filteredMetadata = {
            camera_id: metadata.camera_id,
            camera_model: metadata.camera?.model,
            zoom: metadata.camera?.zoom,
            brightness: metadata.camera?.brightness,
            contrast: metadata.camera?.contrast,
            sharpness: metadata.camera?.sharpness,
            format: metadata.format,
            onboard_vessel_id: artifact.onboard_vessel_id
        }

        Object.keys(filteredMetadata).forEach(key => {
            if (filteredMetadata[key] === undefined || filteredMetadata[key] === null || filteredMetadata[key] === '') {
                delete filteredMetadata[key];
            }
        });

        const data = {
            lat: artifact.location.coordinates[1],
            lon: artifact.location.coordinates[0],
            time: new Date(artifact.timestamp).toISOString(),
            file,
            mmsi: artifact === undefined || artifact === null || Number.isNaN(Number(artifact.imo_number)) ? undefined : Number(artifact.imo_number),
            platform: artifact.unit_id,
            attributes: filteredMetadata
        }

        const prodRes = await seaVisionApi.PostCameraDetection(data, true, false)
        const stagingRes = await seaVisionApi.PostCameraDetection(data, true, true)

        return { prodRes, stagingRes }
    } catch (err) {
        throw err;
    }
}

const UNALLOWED_REGION_GROUPS = ['681c253f9f43051a7748b2c1']

const getAllowedArtifacts = async (artifacts) => {
    const unallowedVesselsIds = await db.qmShared.collection('vessels').find({
        region_group_id: { $in: UNALLOWED_REGION_GROUPS.map(id => new ObjectId(id)) }
    }, { _id: 1 }).toArray().then(vessels => vessels.map(vessel => vessel._id.toString()))

    console.log('[getAllowedArtifacts] unallowedVesselsIds', unallowedVesselsIds)

    return artifacts.filter(artifact => artifact.onboard_vessel_id && !unallowedVesselsIds.includes(artifact.onboard_vessel_id.toString()))
}

const processSeaVisionRequests = async (artifacts) => {
    try {
        if (process.env.NODE_ENV !== 'prod') {
            console.warn('[processSeaVisionRequests] skipping SeaVision requests in non-production environment')
            return;
        }

        if (!artifacts || !Array.isArray(artifacts)) {
            throw new Error('[processSeaVisionRequests] artifacts is a required array');
        }

        const allowedArtifacts = await getAllowedArtifacts(artifacts)
        console.log('[processSeaVisionRequests] allowedArtifacts', allowedArtifacts.length, 'original artifacts', artifacts.length)

        if (allowedArtifacts.length === 0) {
            console.info('[processSeaVisionRequests] no allowed artifacts found')
            return
        }

        console.info('[processSeaVisionRequests] processing sea vision requests for', allowedArtifacts.length, 'artifacts')
        await Promise.all(allowedArtifacts.map(async (artifact) => submitCameraDetection(artifact))).then(console.log);
        await submitRFDetection(allowedArtifacts).then(console.log);

        console.info('[processSeaVisionRequests] sea vision requests successfully processed')
    } catch (err) {
        throw err;
    }
}

module.exports = {
    processSeaVisionRequests
}

// submitCameraDetection(sampleArtifacts[0]).then(console.log).catch(console.error);
// submitRFDetection(sampleArtifacts).then(console.log).catch(console.error);