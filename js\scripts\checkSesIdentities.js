const { SESClient, ListIdentitiesCommand, GetIdentityVerificationAttributesCommand } = require('@aws-sdk/client-ses');
require('dotenv').config();

const sesClient = new SESClient({
    region: process.env.AWS_SES_REGION || 'us-east-1',
    credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
    }
});

async function checkSESIdentities() {
    try {
        console.log('Checking SES verified identities...\n');
        
        // List all identities
        const listCommand = new ListIdentitiesCommand({});
        const identities = await sesClient.send(listCommand);
        
        if (!identities.Identities || identities.Identities.length === 0) {
            console.log('❌ No verified identities found in SES');
            console.log('You need to verify at least one email address or domain in AWS SES Console');
            return;
        }
        
        // Get verification status for each identity
        const verificationCommand = new GetIdentityVerificationAttributesCommand({
            Identities: identities.Identities
        });
        const verification = await sesClient.send(verificationCommand);
        
        console.log('📧 Verified SES Identities:');
        console.log('=' .repeat(50));
        
        identities.Identities.forEach(identity => {
            const status = verification.VerificationAttributes[identity];
            const isVerified = status?.VerificationStatus === 'Success';
            const type = identity.includes('@') ? 'Email' : 'Domain';
            const icon = isVerified ? '✅' : '❌';
            
            console.log(`${icon} ${type}: ${identity} (${status?.VerificationStatus || 'Unknown'})`);
        });
        
        console.log('\n💡 Recommendations:');
        console.log('- Use a verified domain email for AWS_SES_FROM_EMAIL');
        console.log('- If no domains are verified, verify your domain in SES Console');
        console.log('- For testing, you can verify individual email addresses');
        
    } catch (error) {
        console.error('❌ Error checking SES identities:', error.message);
        
        if (error.name === 'UnauthorizedOperation' || error.name === 'AccessDenied') {
            console.log('\n💡 Make sure your IAM user has these permissions:');
            console.log('- ses:ListIdentities');
            console.log('- ses:GetIdentityVerificationAttributes');
        }
    }
}

checkSESIdentities();
