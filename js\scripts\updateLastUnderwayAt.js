require('dotenv').config();
const db = require("../modules/db");
const Vessel = require('../models/Vessel');
const LastLocationsLookup = require('../models/LastLocationsLookup');
const { getTerminalConfirmation, getLocationsCollections } = require('../utils/functions');

const AT_SEA_DISTANCE_M = 1000;

async function run() {
    const allVessels = await Vessel.find({
        is_active: true,
        'home_port_location.coordinates': { $exists: true, $ne: null }
    });

    console.log('allVessels.length', allVessels.length);

    const collections = await getLocationsCollections(db.locationsRaw)
    collections.sort((a, b) => b.name.localeCompare(a.name));

    const updates = [];
    let progress = { total: allVessels.length, current: 0 };

    for (const vessel of allVessels) {
        const record = await LastLocationsLookup.findOne({ vessel_id: vessel._id });
        if (!record || record.last_underway_at !== undefined) {
            progress.current++;
            continue;
        }

        const [homeLng, homeLat] = vessel.home_port_location.coordinates;
        let timestamp = null;

        for (const collection of collections) {
            const pipeline = [
                {
                    $geoNear: {
                        near: { type: "Point", coordinates: [homeLng, homeLat] },
                        distanceField: "distance",
                        maxDistance: 100000, // 100km max to include reasonable range
                        key: "location",
                        spherical: true
                    }
                },
                { $match: { 
                    "metadata.onboardVesselId": vessel._id,
                    distance: { $gte: AT_SEA_DISTANCE_M } // ≥1000m from home port
                }},
                { $sort: { timestamp: -1 } },
                { $limit: 1 },
                {
                    $project: {
                        timestamp: 1,
                        distance: 1
                    }
                }
            ];

            const docs = await collection.aggregate(pipeline).toArray();

            if (docs.length > 0) {
                timestamp = new Date(docs[0].timestamp);
                break;
            }
        }

        updates.push({
            vessel_id: vessel._id,
            last_underway_at: timestamp
        });

        progress.current++;
        console.log(`Progress: ${progress.current}/${progress.total} (${Math.round((progress.current / progress.total) * 100)}%)`);
    }

    console.log('updates', updates.length);

    const confirmation = await getTerminalConfirmation(`Updating ${updates.length} records in DB ${db.lookups.name} in environment ${process.env.NODE_ENV}, continue? (y/n): `);
    if (!confirmation) {
        console.log('User did not confirm, exiting...');
        process.exit(0);
    }

    await Promise.all(updates.map(async (update) => {
        await LastLocationsLookup.findOneAndUpdate({
            vessel_id: update.vessel_id
        }, {
            $set: { last_underway_at: update.last_underway_at }
        }, { upsert: true });
    }));
}

Promise.all([
    new Promise((resolve, reject) => {
        db.qm.once('open', resolve);
        db.qm.on('error', reject);
    }),
    new Promise((resolve, reject) => {
        db.qmai.once('open', resolve);
        db.qmai.on('error', reject);
    }),
    new Promise((resolve, reject) => {
        db.qmShared.once('open', resolve);
        db.qmShared.on('error', reject);
    })
]).then(async () => {
    await run();
    console.log('Success');
    process.exit(0);
});