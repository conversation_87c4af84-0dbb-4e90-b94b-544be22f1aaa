const { KinesisVideoClient, GetDataEndpointCommand, ListStreamsCommand } = require('@aws-sdk/client-kinesis-video');
const { KinesisVideoArchivedMediaClient, ListFragmentsCommand } = require('@aws-sdk/client-kinesis-video-archived-media');
const { createLoggerWithPath } = require('./winston');

const logger = createLoggerWithPath('aws_kinesis');
const regionStreamCache = new Map();

const handleRetryableError = async (err, retryFunction, maxRetries, retryCount = 0, ...args) => {
    if (retryCount >= maxRetries) {
        throw err;
    }

    const retryAfter = parseInt(err?.retryDelay, 10) || 1;
    console.warn(`[awsKinesis.handleRetryableError] retryable error detected, retry ${retryCount + 1}/${maxRetries === Infinity ? '∞' : maxRetries}, waiting ${retryAfter} seconds and retrying`);
    await new Promise((resolve) => setTimeout(resolve, retryAfter * 1000));

    try {
        return await retryFunction.apply(this, args);
    } catch (retryErr) {
        return await handleRetryableError(retryErr, retryFunction, maxRetries, retryCount + 1, ...args);
    }
};

const safeListStreams = async (client, region) => {
    try {
        let nextToken = undefined;
        const names = new Set();
        do {
            const cmd = new ListStreamsCommand({ MaxResults: 100, NextToken: nextToken });
            const res = await client.send(cmd);
            (res.StreamInfoList || []).forEach(info => { if (info?.StreamName) names.add(info.StreamName); });
            nextToken = res?.NextToken;
        } while (nextToken);
        return names;
    } catch (error) {
        if (error.name === 'ClientLimitExceededException') {
            return await handleRetryableError(error, safeListStreams, Infinity, 0, client, region);
        } else if (error?.$metadata?.httpStatusCode >= 500) {
            return await handleRetryableError(error, safeListStreams, 3, 0, client, region);
        }
        throw error;
    }
};

async function listStreamsInRegion(region, vesselUnitIds = null) {
    const cached = regionStreamCache.get(region);
    if (cached && vesselUnitIds) {
        const allVesselsInCache = [...vesselUnitIds].every(unitId => cached.has(unitId));
        if (allVesselsInCache) {
            return cached;
        }
    }

    try {
        const client = new KinesisVideoClient({ region });
        const names = await safeListStreams(client, region);
        regionStreamCache.set(region, names);
        logger.info(`[listStreamsInRegion] listed ${names.size} streams in ${region}`);
        return names;
    } catch (err) {
        logger.error(`[listStreamsInRegion] ${region} failed: ${err.name || 'Error'} ${err.message}`);
        throw err;
    }
}

async function isStreamLive(streamName, region) {
    try {
        const kv = new KinesisVideoClient({ region });
        const endpointRes = await kv.send(new GetDataEndpointCommand({ StreamName: streamName, APIName: 'LIST_FRAGMENTS' }));
        if (!endpointRes?.DataEndpoint) return false;

        const archived = new KinesisVideoArchivedMediaClient({ region, endpoint: endpointRes.DataEndpoint });
        const end = new Date();
        const start = new Date(end.getTime() - 1 * 60 * 1000);

        const fragmentsRes = await archived.send(new ListFragmentsCommand({
            StreamName: streamName,
            MaxResults: 1,
            FragmentSelector: {
                FragmentSelectorType: 'SERVER_TIMESTAMP',
                TimestampRange: { StartTimestamp: start, EndTimestamp: end }
            }
        }));

        if (fragmentsRes?.Fragments && fragmentsRes.Fragments.length > 0) {
            logger.info(`[isStreamLive] stream ${streamName} is LIVE`);
            return true;
        }

        logger.info(`[isStreamLive] stream ${streamName} is OFFLINE`);
        return false;
    } catch (err) {
        if (err.name === 'ClientLimitExceededException') {
            return await handleRetryableError(err, isStreamLive, Infinity, 0, streamName, region);
        } else if (err?.$metadata?.httpStatusCode >= 500) {
            return await handleRetryableError(err, isStreamLive, 3, 0, streamName, region);
        }
        throw err;
    }
}

module.exports = { listStreamsInRegion, isStreamLive };