const { schedule } = require('node-cron');
const Vessel = require('../models/Vessel');
const VesselOnlineLookup = require('../models/VesselOnlineLookup');
const Region = require('../models/Region');
const { listStreamsInRegion, isStreamLive } = require('../modules/awsKinesis');
const { createLoggerWithPath } = require('../modules/winston');
const { postLogToSlack } = require('../modules/notifyLog');

const logger = createLoggerWithPath('vessel_online_processor');
let isRunning = false;

async function getKnownStreamsForVessels(vessels) {
	const regions = await Region.find({ is_live: true });
	const result = [];
	const regionStreamSets = new Map();

	const vesselUnitIds = new Set(vessels.filter(v => v.unit_id).map(v => v.unit_id));

	for (const region of regions) {
		regionStreamSets.set(region.value, await listStreamsInRegion(region.value, vesselUnitIds));
	}

	for (const vessel of vessels) {
		if (!vessel.unit_id) continue;
		const expectedName = vessel.unit_id;
		let found = false;

		for (const region of regions) {
			const set = regionStreamSets.get(region.value);
			if (set.has(expectedName)) {
				result.push({ vessel_id: vessel._id, unit_id: vessel.unit_id, region: region.value, streamName: expectedName });
				found = true;
				break;
			}
		}
		if (!found) {
			logger.info(`[getKnownStreamsForVessels] stream not found for vessel ${vessel.unit_id}`);
		}
	}
	logger.info(`[getKnownStreamsForVessels] known streams for vessels: ${result.length}`);
	return result;
}

async function getLiveStatusesForVessels(vessels) {
	const known = await getKnownStreamsForVessels(vessels);
	logger.info(`[getLiveStatusesForVessels] checking ${known.length} streams in parallel`);

	const promises = known.map(async (k) => {
		const isLive = await isStreamLive(k.streamName, k.region);
		return isLive ? { vessel_id: k.vessel_id, unit_id: k.unit_id, region: k.region, live: true } : null;
	});

	const results = await Promise.all(promises);
	const lives = results.filter(r => r !== null);

	logger.info(`[getLiveStatusesForVessels] all promises resolved - live streams: ${lives.length}`);
	return lives;
}

async function computeVesselOnlineLookup() {
	if (isRunning) {
		logger.info(`[vesselOnlineProcessor] already running`);
		return;
	}
	isRunning = true;
	const start = Date.now();
	logger.info(`[vesselOnlineProcessor] start`);

	try {
		const vessels = await Vessel.find({ is_active: true });
		if (!vessels.length) return;
		logger.info(`[vesselOnlineProcessor] found ${vessels.length} active vessels`);

		const liveStatuses = await getLiveStatusesForVessels(vessels);
		const liveVesselIds = new Set(liveStatuses.map(s => s.vessel_id.toString()));

		const bulk = VesselOnlineLookup.collection.initializeUnorderedBulkOp();
		const now = new Date();

		vessels.forEach(vessel => {
			const isLive = liveVesselIds.has(vessel._id.toString());
			const updateDoc = {
				$set: {
					vessel_id: vessel._id,
					updated_at: now
				}
			};
			if (isLive) {
				updateDoc.$set.last_online_at = now;
			} else {
				updateDoc.$setOnInsert = {
					last_online_at: now
				};
			}
			bulk.find({ vessel_id: vessel._id }).upsert().updateOne(updateDoc);
		});

		await bulk.execute();
		logger.info(`[vesselOnlineProcessor] updated ${vessels.length} vessels (${liveStatuses.length} live, ${vessels.length - liveStatuses.length} offline) in ${Date.now() - start}ms`);
	} catch (err) {
		logger.error(`[vesselOnlineProcessor] error: ${err?.message}`);
		postLogToSlack({
			severity: 'fatal',
			message: 'Fatal error in vessel online processor',
			stack: err.stack
		})
	} finally {
		isRunning = false;
	}
}

schedule('0 */5 * * * *', computeVesselOnlineLookup, { scheduled: true, timezone: 'UTC' });
module.exports = { computeVesselOnlineLookup };