const { createLoggerWithPath } = require('../modules/winston');
const { calculateDistanceInMeters, getVesselInfoByUnitId } = require('../utils/functions');
const io = require('../modules/io');
const { getLocationRawCollection } = require('../models/LocationRaw');
const { default: mongoose } = require('mongoose');
const { getLocationOptimizedCollection } = require('../models/LocationOptimized');
const LastLocationsLookup = require('../models/LastLocationsLookup');

const locationUpdateInterval = 60000 // in milliseconds
const MAX_COORDINATES = 5
const vesselCoordinatesCache = {}
const AT_SEA_DISTANCE_M = 1000;

function updateVesselLocationCache(vesselId, locationData) {
    if (!vesselCoordinatesCache[vesselId]) {
        vesselCoordinatesCache[vesselId] = [];
    }

    const vesselCache = vesselCoordinatesCache[vesselId];

    if (vesselCache.length >= MAX_COORDINATES) {
        vesselCache.shift();
    }

    vesselCache.push(locationData);
}

function getVesselLocationCache(vesselId) {
    return vesselCoordinatesCache[vesselId] || [];
}

function checkIsStationaryCoordinate(vesselId, latitude, longitude) {
    const WINDOW_SIZE = 4;
    const DISTANCE_THRESHOLD = 50;
    const STATIONARY_CHECK_MINUTES = 5;

    const cachedCoordinates = getVesselLocationCache(vesselId);

    if (cachedCoordinates.length === 0) {
        return false;
    }

    const minutesAgo = new Date(Date.now() - (STATIONARY_CHECK_MINUTES * 60 * 1000));
    const recentPoints = cachedCoordinates
        .filter(point => new Date(point.timestamp) > minutesAgo)
        .slice(-WINDOW_SIZE);

    if (recentPoints.length === 0) {
        return false;
    }

    const distances = recentPoints.map(point =>
        calculateDistanceInMeters(
            latitude,
            longitude,
            point.latitude,
            point.longitude
        )
    );

    const isStationary = distances.every(distance => {
        return distance <= DISTANCE_THRESHOLD;
    });

    return isStationary;
}

const lastReadings = {}

async function processIotGpsMessage(topic, message, region) {
    if (!topic.endsWith('/gps/status')) return;
    // console.log('[GPS Service] processing message from topic', topic, 'in region', region)
    const unit_id = topic.split('/').shift();

    const logger = createLoggerWithPath(`mqtt/${region}/${unit_id}`);

    try {
        const decoder = new TextDecoder('utf-8');
        const messageString = decoder.decode(message);
        const data = JSON.parse(messageString);

        if (data.valid_gnss_fix) {
            const { latitude, longitude, ground_speed: groundSpeed, heading_motion: headingMotion, accuracy_heading: accuracyHeading } = data;

            const timestampMs = Number(data.header.stamp.sec) * 1000
            const timestampISO = new Date(timestampMs).toISOString()
            const isoSplit = timestampISO.split("-");
            const yearMonth = isoSplit[0] + "-" + isoSplit[1];

            // TODO: update socket
            io.emit(`${unit_id}/gps`, {
                vesselName: unit_id,
                latitude,
                longitude,
                groundSpeed,
                headingMotion,
                accuracyHeading,
                timestamp: timestampMs,
                metadata: data
            });


            const lastReading = lastReadings[unit_id];
            const lastProcessedWithinInterval = lastReading && lastReading.getTime() + locationUpdateInterval > new Date().getTime();

            if (lastProcessedWithinInterval) return;

            lastReadings[unit_id] = new Date();

            const rawCollection = await getLocationRawCollection(yearMonth);
            if (!rawCollection) throw new Error(`[GPS Service] Unable to find raw collection for ${unit_id}`);

            const optimizedCollection = await getLocationOptimizedCollection(yearMonth);
            if (!optimizedCollection) throw new Error(`[GPS Service] Unable to find optimized collection for ${unit_id}`);

            /** optimized to use local variable instead of querying db every invocation */
            // const lastRecord = await collection.findOne({
            //     timestamp: { $gt: new Date().getTime() - locationUpdateInterval },
            // });
            // if (lastRecord) return;

            const vessel = await getVesselInfoByUnitId(unit_id, { _id: 1, home_port_location: 1 });
            let onboardVesselId = null;
            if (vessel && vessel._id) {
                onboardVesselId = vessel._id;
            }
            const isStationary = onboardVesselId ? checkIsStationaryCoordinate(onboardVesselId, latitude, longitude) : false;

            logger.info(`[GPS Service] Received GPS Coordinates for unit ${unit_id} in Region ${region}: Latitude = ${latitude}, Longitude = ${longitude}, Ground Speed = ${groundSpeed}, Is Stationary = ${isStationary}, Heading Motion = ${headingMotion}, Accuracy Heading = ${accuracyHeading}`);

            // TODO: remove legacy implementation once new implementation is stable in production
            const legacyLocationData = {
                latitude,
                longitude,
                groundSpeed,
                isStationary,
                headingMotion,
                accuracyHeading,
                onboardVesselId,
                metadata: data,
                timestamp: timestampISO
            };

            if (onboardVesselId) updateVesselLocationCache(onboardVesselId, legacyLocationData);

            // TODO: update socket
            // io.emit(`${unit_id}_location/insert`, { unit_id, ...dbRes.toObject() })

            const rawLocationData = {
                location: {
                    type: "Point",
                    coordinates: [longitude, latitude]
                },
                groundSpeed,
                isStationary,
                headingMotion,
                accuracyHeading,
                metadata: {
                    onboardVesselId,
                    unitId: unit_id
                },
                details: data,
                timestamp: timestampISO
            };

            const rawLocation = await rawCollection.create(rawLocationData)

            // Check if vessel is underway
            let isUnderway = false;
            if (vessel?.home_port_location?.coordinates) {
                const [homeLng, homeLat] = vessel.home_port_location.coordinates;
                const distance = await calculateDistanceInMeters(homeLat, homeLng, latitude, longitude);
                isUnderway = distance >= AT_SEA_DISTANCE_M;
            }

            const updateDoc = {
                $set: {
                    last_location_id: rawLocation._id,
                    last_location_timestamp: timestampISO,
                    collection: rawCollection.collection.name,
                    db: rawCollection.db.name,
                    data: rawLocationData
                }
            };
            if (isUnderway) {
                updateDoc.$set.last_underway_at = new Date(timestampISO);
            } else {
                updateDoc.$setOnInsert = {
                    last_underway_at: null
                };
            }

            await LastLocationsLookup.findOneAndUpdate({ vessel_id: onboardVesselId }, updateDoc, { upsert: true })

            const optimizedLocationData = {
                location: {
                    type: "Point",
                    coordinates: [longitude, latitude]
                },
                groundSpeed,
                isStationary,
                headingMotion,
                accuracyHeading,
                metadata: {
                    onboardVesselId,
                    unitId: unit_id
                },
                // details: data,
                timestamp: timestampISO
            };

            await optimizedCollection.create(optimizedLocationData)

        } else {
            logger.error(`[GPS Service] Received Invalid GPS Coordinates for Vessel ${unit_id} in Region ${region}: Latitude = ${data.latitude}, Longitude = ${data.longitude}`);
        }
    } catch (error) {
        logger.error(`[GPS Service] Error processing MQTT message for Vessel ${unit_id} in Region ${region}`, JSON.stringify(error));
        throw error;
    }
}

module.exports = {
    processIotGpsMessage
}
