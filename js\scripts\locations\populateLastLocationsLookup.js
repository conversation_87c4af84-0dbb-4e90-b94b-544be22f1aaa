require('dotenv').config();
const db = require("../../modules/db");
const { getTerminalConfirmation } = require('../../utils/functions');

async function run() {
    const allVessels = await db.qmShared.collection('vessels').find({}).toArray()

    console.log('allVessels.length', allVessels.length)

    const rawCollectionNames = (await db.locationsRaw.db.listCollections().toArray()).filter(c => !c.name.startsWith('system.')).map(c => c.name).sort((a, b) => b.localeCompare(a))

    const lastLocations = []
    console.log('rawCollectionNames', rawCollectionNames)
    let progress = { total: allVessels.length, current: 0 }
    for (const vessel of allVessels) {
        let lastLocation = null
        let collection;
        for (const rawCollectionName of rawCollectionNames) {
            collection = rawCollectionName;
            lastLocation = await db.locationsRaw.collection(rawCollectionName).findOne({
                'metadata.onboardVesselId': vessel._id
            }, { sort: { timestamp: -1 } })
            if (lastLocation) break
        }
        if (!lastLocation) {
            progress.current++
            console.warn(`No last location found for vessel ${vessel._id}`)
            continue
        }
        lastLocations.push({
            vessel_id: lastLocation.metadata.onboardVesselId,
            last_location_id: lastLocation._id,
            last_location_timestamp: lastLocation.timestamp,
            collection: collection,
            db: db.locationsRaw.name,
            data: lastLocation
        })
        progress.current++
        console.log(`Progress: ${progress.current}/${progress.total} (${Math.round((progress.current / progress.total) * 100)}%)`)
    }

    console.log('lastLocations', lastLocations)

    const confirmation = await getTerminalConfirmation(`Upserting ${lastLocations.length} records in DB ${db.lookups.name} in environment ${process.env.NODE_ENV}, continue? (y/n): `)
    if (!confirmation) {
        console.log('User did not confirm, exiting...');
        process.exit(0);
    }

    await Promise.all(lastLocations.map(async (lastLocation) => {
        await db.lookups.collection('last_locations_lookup').findOneAndUpdate({
            vessel_id: lastLocation.vessel_id
        }, {
            $set: lastLocation
        }, { upsert: true })
    }))
}

Promise.all([
    new Promise((resolve, reject) => {
        db.lookups.once('open', resolve);
        db.lookups.on('error', reject);
    }),
    new Promise((resolve, reject) => {
        db.qmShared.once('open', resolve);
        db.qmShared.on('error', reject);
    })
]).then(async () => {
    await run()
    console.log('Success')
    process.exit(0)
})