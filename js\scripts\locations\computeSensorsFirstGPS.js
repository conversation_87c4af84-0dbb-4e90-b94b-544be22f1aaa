require('dotenv').config();
const { listThings } = require('../../modules/awsIot');
const db = require("../../modules/db");
const fs = require('fs');
const path = require('path');

const convertToCSV = (sensorFirstGPSList) => {
    const header = ['Unid ID', 'Active Vessel ID', 'Active Vessel Name', 'In Production', 'First GPS Timestamp'].join(',')
    const data = sensorFirstGPSList.map(item => Object.values(item).join(','))
    const csv = `${header}\n${data.join('\n')}`
    return csv
}

const getSensorsList = async () => {
    const sensorsList = (await listThings()).map(sensor => sensor.things.filter(thing => thing.thingTypeName === 'smartmast')).flat()
    const registeredVessels = await db.qmShared.collection('vessels').find({}).toArray()

    const sensors = sensorsList.map(sensor => {
        return {
            unit_id: sensor.thingName,
            vessel_id: registeredVessels.find(vessel => vessel.unit_id === sensor.thingName)?._id,
            vessel_name: registeredVessels.find(vessel => vessel.unit_id === sensor.thingName)?.name,
            is_active: registeredVessels.find(vessel => vessel.unit_id === sensor.thingName)?.is_active || false,
        }
    }).sort((a, b) => a.is_active ? -1 : 1)

    return sensors
}

const findSensorFirstGPS = async (collectionsList, sensor) => {
    for (const collection of collectionsList) {
        const sensorFirstGPS = await db.locationsRaw.collection(collection).findOne({
            'metadata.unitId': sensor.unit_id
        })
        if (sensorFirstGPS) {
            return sensorFirstGPS
        }
    }
    return null
}

async function run() {
    console.log('Getting sensors list')
    const sensorsList = await getSensorsList()
    console.log('sensorsList', sensorsList)

    const collectionsList = (await db.locationsRaw.db.listCollections().toArray()).filter(c => !c.name.startsWith('system.')).map(c => c.name).sort((a, b) => a.localeCompare(b))
    console.log('collectionsList', collectionsList)

    const sensorFirstGPSList = (await Promise.all(sensorsList.map(async (sensor) => {
        const sensorFirstGPS = await findSensorFirstGPS(collectionsList, sensor)
        return {
            ...sensor,
            first_gps_timestamp: sensorFirstGPS?.timestamp || null
        }
    }))).sort((a, b) => b.first_gps_timestamp - a.first_gps_timestamp)

    console.log('sensorFirstGPS', sensorFirstGPSList)

    const csv = convertToCSV(sensorFirstGPSList)

    const filePath = path.join(__dirname, '../temp', 'sensors-first-gps-timestamps.csv')
    fs.writeFileSync(filePath, csv)
    console.log('CSV file saved at', filePath)
}

Promise.all([
    new Promise((resolve, reject) => {
        db.locationsRaw.once('open', resolve);
        db.locationsRaw.on('error', reject);
    }),
    new Promise((resolve, reject) => {
        db.qmShared.once('open', resolve);
        db.qmShared.on('error', reject);
    })
]).then(async () => {
    await run()
    console.log('Success')
    process.exit(0)
})