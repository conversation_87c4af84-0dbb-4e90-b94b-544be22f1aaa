const url = 'https://infra.quartermaster.us';
let failCount = 0;

const count = {
    total: 0,
    failed: 0,
    ok: 0,
    nonOk: 0,
    successPercentage: () => this.ok / this.total * 100,
}

async function ping() {
    try {
        count.total++;
        const start = Date.now();
        const response = await fetch(url);
        const time = Date.now() - start;

        const isOk = response.ok;

        if (isOk) {
            count.ok++;
        } else {
            count.nonOk++;
        }

        console.log(
            `[${new Date().toLocaleTimeString()}] ${isOk ? '✅' : '❌'} ${response.status} (${response.statusText}) - ${time}ms`
        );
    } catch (err) {
        count.failed++;
        console.log(
            `[${new Date().toLocaleTimeString()}] ❌ Request failed (${failCount} total fails): ${err.message}`
        );
    }
}

// Run every 1 second
setInterval(ping, 1000);

// Print stats every 10 seconds
setInterval(() => {
    console.log(`[${new Date().toLocaleTimeString()}] Failures: ${count.failed}, Successes: ${count.ok}, Non-OK: ${count.nonOk}, Success Percentage: ${count.ok / count.total * 100}%`);
}, 10000);
