
const { createLoggerWithPath } = require('../modules/winston');
const io = require('../modules/io');
const { getVesselInfoByUnitId, calculateDistanceInMeters, computeTrueBearingDeg, getCountryByMmsi } = require('../utils/functions');
const AisMmsiLookup = require('../models/AisMmsiLookup');
const mongoose = require('mongoose');
const { getAisRawCollection } = require('../models/AisRaw');

async function isValidDistance({ hostLat, hostLon, vesselLat, vesselLon, thresholdKm = 100 }) {
    try {
        // Check if all coordinates are valid numbers
        if (!hostLat || !hostLon || !vesselLat || !vesselLon ||
            isNaN(hostLat) || isNaN(hostLon) || isNaN(vesselLat) || isNaN(vesselLon)) {
            return false; // Invalid coordinates default to false
        }

        const distanceMeters = await calculateDistanceInMeters(hostLat, hostLon, vesselLat, vesselLon);
        const distanceKm = distanceMeters / 1000;

        return distanceKm <= thresholdKm;
    } catch (error) {
        console.error('Error calculating portal distance validation:', error);
        return false;
    }
}

async function processIotAisMessage(topic, message, region) {
    if (!topic.endsWith('/sdr/status')) return;

    const unit_id = topic.split('/').shift();

    const logger = createLoggerWithPath(`mqtt/ais/${region}/${unit_id}`);

    try {
        const decoder = new TextDecoder('utf-8');
        const messageString = decoder.decode(message);
        const data = JSON.parse(messageString);

        const { ais_messages } = data;

        const vessel = await getVesselInfoByUnitId(unit_id, { _id: 1 });
        let onboard_vessel_id = null;
        if (vessel && vessel._id) {
            onboard_vessel_id = vessel._id;
        }

        for (const message of ais_messages) {

            var { nav_latitude: latitude, nav_longitude: longitude, mmsi, name, host_location_latitude, host_location_longitude } = message;

            const timestampISO = new Date(message.timestamp).toISOString()
            const isoSplit = timestampISO.split("-");
            const yearMonth = isoSplit[0] + "-" + isoSplit[1];

            if (!mmsi) return logger.error(`[AIS Service] Invalid AIS message for unit ${unit_id} in Region ${region}: ${JSON.stringify(message)}: mmsi is ${mmsi}`);
            // if (!name) return logger.error(`[AIS Service] Invalid AIS message for unit ${unit_id} in Region ${region}: ${JSON.stringify(message)}: name is ${name}`);
            if (!latitude || !longitude) return logger.error(`[AIS Service] Invalid AIS message for unit ${unit_id} in Region ${region}: ${JSON.stringify(message)}: latitude is ${latitude}, longitude is ${longitude}`);

            logger.info(`[AIS Service] Received AIS for unit ${unit_id} in Region ${region}: Latitude = ${latitude}, Longitude = ${longitude}`);

            const isPortalValid = await isValidDistance({
                hostLat: host_location_latitude,
                hostLon: host_location_longitude,
                vesselLat: latitude,
                vesselLon: longitude,
                thresholdKm: 100 // 100km threshold
            });

            message.portal_valid = isPortalValid;
            message.portal_registry_country = getCountryByMmsi(mmsi);
            message.portal_true_bearing_deg = computeTrueBearingDeg({
                hostLat: host_location_latitude,
                hostLon: host_location_longitude,
                vesselLat: latitude,
                vesselLon: longitude
            });

            const objId = new mongoose.Types.ObjectId();

            // const structuredData = {
            //     _id: objId,
            //     onboard_vessel_id,
            //     location: {
            //         type: 'Point',
            //         coordinates: [longitude, latitude]
            //     },
            //     mmsi,
            //     name: name || null,
            //     timestamp: new Date(message.timestamp),
            //     metadata: {
            //         ...data,
            //         message,
            //         ais_messages: null,
            //         _id: objId
            //     }
            // }

            const structuredDataRaw = {
                _id: objId,
                location: {
                    type: 'Point',
                    coordinates: [longitude, latitude]
                },
                name: name || null,
                timestamp: new Date(message.timestamp),
                metadata: {
                    onboard_vessel_id,
                    mmsi,
                    unit_id
                },
                details: {
                    ...data,
                    message,
                    ais_messages: null
                }
            }

            const collectionRaw = await getAisRawCollection(yearMonth);
            if (!collectionRaw) return logger.error(`[AIS Service] Unable to find raw collection for ${yearMonth}`);

            const dbRecordRaw = await collectionRaw.create(structuredDataRaw)

            io.emit(`${unit_id}/ais`, structuredDataRaw);

            if (isPortalValid && mmsi && onboard_vessel_id) {
                await AisMmsiLookup.findOneAndUpdate({ mmsi, onboard_vessel_id }, {
                    $set: {
                        last_message_id: dbRecordRaw._id,
                        last_message_timestamp: structuredDataRaw.timestamp,
                        db: collectionRaw.db.name,
                        collection: collectionRaw.collection.name,
                        data: structuredDataRaw
                    }
                }, { upsert: true })
            } else {
                logger.info(`[AIS Service] Lookup insertion skipped. objId: ${objId}, mmsi: ${mmsi}, onboard_vessel_id: ${onboard_vessel_id}, portal_valid: ${isPortalValid}`);
            }
        }
    } catch (error) {
        logger.error(`[AIS Service] Error processing MQTT message for Vessel ${unit_id} in Region ${region}`, JSON.stringify(error));
        throw error;
    }
}

module.exports = {
    processIotAisMessage
}