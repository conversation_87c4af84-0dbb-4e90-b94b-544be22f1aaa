const mongoose = require('mongoose');
const db = require('../modules/db');

const lastLocationsLookupSchema = new mongoose.Schema({
    vessel_id: { type: mongoose.Schema.Types.ObjectId, required: true, unique: true },
    last_location_id: { type: mongoose.Schema.Types.ObjectId, required: true },
    last_location_timestamp: { type: Date, required: true },
    last_underway_at: { type: Date, required: false },
    collection: { type: String, required: true },
    db: { type: String, required: true },
    data: { type: Object, required: true }
});

const LastLocationsLookup = db.lookups.model('LastLocationsLookup', lastLocationsLookupSchema, 'last_locations_lookup');

module.exports = LastLocationsLookup