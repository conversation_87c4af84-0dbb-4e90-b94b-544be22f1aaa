const { WorkMailClient, ListOrganizationsCommand, DescribeOrganizationCommand, ListUsersCommand } = require('@aws-sdk/client-workmail');
require('dotenv').config();

const workMailClient = new WorkMailClient({
    region: 'us-east-1', // WorkMail is available in limited regions
    credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
    }
});

async function getWorkMailInfo() {
    try {
        console.log('🔍 Checking WorkMail Organizations...\n');
        
        // List organizations
        const listCommand = new ListOrganizationsCommand({});
        const organizations = await workMailClient.send(listCommand);
        
        if (!organizations.OrganizationSummaries || organizations.OrganizationSummaries.length === 0) {
            console.log('❌ No WorkMail organizations found');
            console.log('💡 You need to create a WorkMail organization first');
            return;
        }
        
        for (const org of organizations.OrganizationSummaries) {
            console.log('📧 WorkMail Organization Found:');
            console.log('=' .repeat(50));
            console.log(`📋 Organization ID: ${org.OrganizationId}`);
            console.log(`🏢 Alias: ${org.Alias}`);
            console.log(`🌐 State: ${org.State}`);
            
            // Get detailed organization info
            try {
                const describeCommand = new DescribeOrganizationCommand({
                    OrganizationId: org.OrganizationId
                });
                const details = await workMailClient.send(describeCommand);
                
                console.log(`🔗 Web App URL: https://${org.Alias}.awsapps.com/mail`);
                console.log(`📧 Default Domain: ${details.DefaultMailDomain}`);
                
                // List users in this organization
                const usersCommand = new ListUsersCommand({
                    OrganizationId: org.OrganizationId
                });
                const users = await workMailClient.send(usersCommand);
                
                if (users.Users && users.Users.length > 0) {
                    console.log('\n👥 WorkMail Users:');
                    users.Users.forEach(user => {
                        console.log(`   📧 ${user.Email} (${user.Name}) - ${user.State}`);
                    });
                } else {
                    console.log('\n❌ No users found in this organization');
                    console.log('💡 You need to create users to send emails');
                }
                
            } catch (detailError) {
                console.log(`⚠️  Could not get details: ${detailError.message}`);
            }
            
            console.log('\n🔑 To access inbox:');
            console.log(`   1. Go to: https://${org.Alias}.awsapps.com/mail`);
            console.log(`   2. Login with your WorkMail user credentials`);
            console.log(`   3. Username: [your-username] (e.g., alerts)`);
            console.log(`   4. Password: [password you set when creating user]`);
            console.log('\n');
        }
        
    } catch (error) {
        console.error('❌ Error checking WorkMail:', error.message);
        
        if (error.name === 'UnauthorizedOperation' || error.name === 'AccessDenied') {
            console.log('\n💡 Make sure your IAM user has WorkMail permissions:');
            console.log('   - workmail:ListOrganizations');
            console.log('   - workmail:DescribeOrganization');
            console.log('   - workmail:ListUsers');
        }
    }
}

getWorkMailInfo();
