# Application
PORT=5002
NODE_ENV=local | prod 
JWT_SECRET=''
APP_URL='http://localhost:3000'
API_URL='http://localhost:5001/api'

# AWS
AWS_ACCESS_KEY_ID=''
AWS_SECRET_ACCESS_KEY=''

AWS_COMPRESSED_ITEMS_BUCKET=''
AWS_COMPRESSED_ITEMS_REGION=''

# AWS SES Configuration
AWS_SES_REGION=''
AWS_SES_FROM_EMAIL=''

# MQTT
MQTT_CLIENT_ID=''
MQTT_BROKER=''

# Database
MONGO_URI=''

# NodeMailer Settings for Google Account
MAIL_USER=''
CLIENT_ID=''
CLIENT_SECRET=''
REDIRECT_URI=''
REFRESH_TOKEN=''

# Porkbun Mail Configuration
PORKBUN_MAIL_USER=''
PORKBUN_MAIL_USERNAME=''
PORKB<PERSON>_MAIL_PASSWORD=''

# Slack
SLACK_LOGS_WEBHOOK_URL = ''

# Python
FLASK_API_URL='http://localhost:5010'

# CloudFront
CLOUDFRONT_KEY_PAIR_ID=''
CLOUDFRONT_PRIVATE_KEY=''