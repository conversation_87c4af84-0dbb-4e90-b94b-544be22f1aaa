const mongoose = require('mongoose');
const db = require("../modules/db");

let VesselAisRaw = {}

const getAisRawCollection = async (month) => {
    const collection = month;

    if (VesselAisRaw[collection]) return VesselAisRaw[collection]

    const VesselAisRawSchema = new mongoose.Schema({
        location: {
            type: {
                type: String,
                enum: ['Point'],
                required: true
            },
            coordinates: {
                type: [Number],
                required: true
            }
        },
        name: { type: String, required: false },
        metadata: { type: Object, required: true },
        details: { type: Object, required: true },
        timestamp: { type: Date, required: true, index: true },
        creation_timestamp: { type: Date, required: false, default: () => new Date() }
    }, { minimize: false, timeseries: { timeField: 'timestamp', metaField: 'metadata', granularity: 'seconds' } });

    const model = db.aisRaw.model(collection, VesselAisRawSchema, collection);
    await model.createCollection()

    VesselAisRaw[collection] = model

    return model
}

module.exports = {
    getAisRawCollection
};