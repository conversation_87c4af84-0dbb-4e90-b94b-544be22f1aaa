const { downloadListOrFavorites } = require("./listDownloadService");
const { sendEmail } = require("../modules/email");
const { LIST_DOWNLOAD_EMAIL_CONTENT, LIST_DOWNLOAD_FAILED_EMAIL_CONTENT } = require("../utils/Email");
const { postLogToSlack } = require("../modules/notifyLog");
const db = require("../modules/db");
const mongoose = require("mongoose");
const List = require("../models/List");

async function downloadListListener(socket, data) {
    let userEmail = null;
    let userName = null;
    let type = null;
    let listName = null;

    try {
        let parsedData;

        if (typeof data === "string") {
            parsedData = JSON.parse(data);
        } else {
            parsedData = data;
        }

        const { type: reqType, listId, userId } = parsedData;
        type = reqType;

        if (!type || !userId) {
            throw new Error("Missing required fields: type and userId are required");
        }

        if (type !== "list" && type !== "favorite") {
            throw new Error(`Invalid type: ${type}. Must be "list" or "favorite"`);
        }

        if (type === "list" && !listId) {
            throw new Error("listId is required when type is 'list'");
        }

        if (type === "list" && listId) {
            const list = await List.findById(listId).select("name").lean();
            if (list) listName = list.name;
        }

        const user = await db.qm.collection("users").findOne(
            { _id: new mongoose.Types.ObjectId(userId) },
            { projection: { email: 1, name: 1 } }
        );

        if (user) {
            userEmail = user.email;
            userName = user.name;
        }

        const serviceType = type === "favorite" ? "favorites" : "list";
        const idOrUserId = type === "list" ? listId : userId;
        const result = await downloadListOrFavorites(serviceType, idOrUserId, userId);

        const emailSubject = type === "list"
            ? `${listName ? `${listName}` : ""} List Download Ready`
            : "Favorites Download Ready";
        const emailHtml = LIST_DOWNLOAD_EMAIL_CONTENT(
            result.userName || userName,
            type,
            result.signedUrl,
            result.downloadedCount,
            result.downloadErrors,
            result.archiveKey,
            listName
        );

        await sendEmail(result.userEmail, emailSubject, emailHtml);
    } catch (error) {
        console.error("[List Download Listener] Error:", error);

        await postLogToSlack({
            severity: 'fatal',
            message: error.message,
            stack: error.stack
        });

        if (userEmail) {
            try {
                const emailSubject = type === "list"
                    ? `${listName ? `${listName}` : ""} List Download Failed`
                    : "Favorites Download Failed";
                const emailHtml = LIST_DOWNLOAD_FAILED_EMAIL_CONTENT(
                    userName || "User",
                    type,
                    error.message || "An unexpected error occurred",
                    listName
                );
                await sendEmail(userEmail, emailSubject, emailHtml);
            } catch (emailError) {
                console.error("[List Download Listener] Failed to send error email:", emailError);
            }
        }
    }
}

module.exports = {
    downloadListListener,
};
