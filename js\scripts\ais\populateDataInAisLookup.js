require('dotenv').config();
const db = require("../../modules/db");
const { getTerminalConfirmation } = require('../../utils/functions');

const updateAis = (ais) => {
    const { unitId, metadata, onboard_vessel_id, mmsi, ...rest } = ais;

    return {
        ...rest,
        metadata: {
            onboard_vessel_id,
            unit_id: unitId,
            mmsi
        },
        details: metadata || {}
    };
};

async function run() {
    console.log('Running populateDataInAisLookup')

    const aisLookups = await db.lookups.collection('ais_mmsi_lookup').find({ data: null }).toArray()
    console.log('aisLookups.length', aisLookups.length)

    const aisCollections = aisLookups.reduce((acc, aisLookup) => {
        if (!acc[aisLookup.collection]) {
            acc[aisLookup.collection] = []
        }
        acc[aisLookup.collection].push(aisLookup)
        return acc
    }, {})

    const updateQueries = []
    for (const collection of Object.keys(aisCollections)) {
        const ais = await db.qmAis.collection(collection).find({ _id: { $in: aisCollections[collection].map(aisLookup => aisLookup.last_message_id) } }).toArray()
        console.log('ais', ais.length)
        if (ais.length !== aisCollections[collection].length) {
            throw new Error('ais.length !== aisCollections[collection].length')
        }
        const queries = aisCollections[collection].map(aisLookup => {
            const aisRecord = ais.find(aisRecord => aisRecord._id.toString() === aisLookup.last_message_id.toString())
            if (!aisRecord) {
                throw new Error('aisRecord not found')
            }
            aisRecord.unitId = collection.split('_ais')[0]
            const updatedAis = updateAis(aisRecord)
            return {
                updateOne: {
                    filter: { _id: aisLookup._id },
                    update: { $set: { data: updatedAis } }
                }
            }
        })
        updateQueries.push(...queries)
    }

    console.log('updateQueries.length', updateQueries.length)

    if (updateQueries.length !== aisLookups.length) {
        throw new Error('updateQueries.length !== aisLookups.length')
    }

    console.log('updateQueries', JSON.stringify(updateQueries[0]))

    const confirmation = await getTerminalConfirmation(`Updating ${updateQueries.length} records in database ${db.lookups.name} collection ais_mmsi_lookup, continue? (y/n): `);
    if (!confirmation) {
        console.log('User did not confirm, exiting...');
        process.exit(0);
    }

    await db.lookups.collection('ais_mmsi_lookup').bulkWrite(updateQueries)

    console.log('Success')
    process.exit(0)
}

Promise.all([
    new Promise((resolve, reject) => {
        db.lookups.once('open', resolve);
        db.lookups.on('error', reject);
    }),
    new Promise((resolve, reject) => {
        db.qmAis.once('open', resolve);
        db.qmAis.on('error', reject);
    })
]).then(() => {
    setTimeout(() => {
        run()
    }, 1000);
}).catch(err => {
    console.error('Failed to establish database connections:', err);
    process.exit(1);
});