require('dotenv').config();
const db = require("../../modules/db");
const { getTerminalConfirmation } = require('../../utils/functions');

function toRadians(deg) {
    return deg * Math.PI / 180;
}

function toDegrees(rad) {
    return rad * 180 / Math.PI;
}

function computeTrueBearingDeg({ hostLat, hostLon, vesselLat, vesselLon }) {
    if ([hostLat, hostLon, vesselLat, vesselLon].some(v => typeof v !== 'number')) {
        console.warn('[computeTrueBearingDeg] Invalid coordinates');
        return null;
    }

    const φ1 = toRadians(hostLat);
    const φ2 = toRadians(vesselLat);
    const Δλ = toRadians(vesselLon - hostLon);

    const y = Math.sin(Δλ) * Math.cos(φ2);
    const x = Math.cos(φ1) * Math.sin(φ2) -
        Math.sin(φ1) * Math.cos(φ2) * Math.cos(Δλ);

    let θ = Math.atan2(y, x); // in radians
    let bearingDeg = (toDegrees(θ) + 360) % 360; // normalize to 0–360°

    return bearingDeg;
}

async function run() {
    const confirmation = await getTerminalConfirmation(`Executing script on db ${db.aisRaw.name}, continue? (y/n): `)
    if (!confirmation) {
        console.log('User did not confirm, exiting...');
        process.exit(0);
    }

    const yearMonth = '2025-09'
    const collection = db.aisRaw.collection(yearMonth)

    let startTimestamp = new Date('2025-09-01T00:00:00.000Z')

    while (startTimestamp.getTime() < new Date('2025-10-01T00:00:00.000Z').getTime()) {
        const endTimestamp = new Date(startTimestamp.getTime() + 24 * 60 * 60 * 1000)

        console.log('processing for range', startTimestamp, endTimestamp)

        const currentDayAis = await collection.find({
            timestamp: {
                $gte: startTimestamp,
                $lt: endTimestamp
            }
        }).toArray()

        console.log('currentDayAis', currentDayAis.length)

        if (currentDayAis.length === 0) {
            console.log('no ais found for range', startTimestamp, endTimestamp)
            startTimestamp = endTimestamp
            continue
        }

        const updatedAis = currentDayAis.map(ais => {

            var { nav_latitude, nav_longitude, host_location_latitude, host_location_longitude } = ais.details.message;
            ais.details.message.portal_true_bearing_deg = computeTrueBearingDeg({
                hostLat: host_location_latitude,
                hostLon: host_location_longitude,
                vesselLat: nav_latitude,
                vesselLon: nav_longitude
            });

            return ais;
        })

        console.log('updatedAis', updatedAis.length)

        console.log('deleting records for range', startTimestamp, endTimestamp)
        await collection.deleteMany({
            timestamp: {
                $gte: startTimestamp,
                $lt: endTimestamp
            }
        })

        console.log('inserting records for range', startTimestamp, endTimestamp)
        await collection.insertMany(updatedAis)

        startTimestamp = endTimestamp
    }
}

Promise.all([
    new Promise((resolve, reject) => {
        db.aisRaw.once('open', resolve);
    })
]).then(async () => {
    await run();
    console.log('Success')
    process.exit(0)
}).catch(err => {
    console.error('Failed to establish database connections:', err);
    process.exit(1);
});