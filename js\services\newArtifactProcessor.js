const db = require('../modules/db');
const io = require('../modules/io');
const { getBearingRange, dateFromObjectId } = require("../utils/functions");
const { s3 } = require('../modules/awsS3');
const { processNotificationAlerts } = require("./notificationAlertsProcessor");
const { buildThumbnailImage } = require("../modules/awsS3");
const { processSeaVisionRequests } = require("./processSeaVisionRequests");
const { postLogToSlack } = require('../modules/notifyLog');
const evaluatorApi = require('../modules/evaluatorApi');

const detectingNewArtifactsPeriodic = 300000;
const processingSettingsLatestArtifactIdName = 'latest_processed_artifact_id';

async function getLastProcessedArtifactId() {
    var latestArtifactId = await db.qmai.collection('processing_settings').findOne({ name: processingSettingsLatestArtifactIdName });

    if (!latestArtifactId) {
        // note: this is not the best way to do this, but it's the only way to get the latest artifact
        const latestArtifact = await db.qmai.collection('analysis_results').findOne({}, { sort: { timestamp: -1 } });
        console.log('[getLastProcessedArtifactId] latestArtifact', latestArtifact)
        const res = await db.qmai.collection('processing_settings').insertOne({ name: processingSettingsLatestArtifactIdName, value: latestArtifact._id });
        latestArtifactId = await db.qmai.collection('processing_settings').findOne({ _id: res.insertedId });
    }

    if (!latestArtifactId?.value) {
        throw new Error('[getLastProcessedArtifactId] No latest timestamp found');
    }

    console.log('[getLastProcessedArtifactId] latestArtifactId', latestArtifactId)

    return latestArtifactId.value;
}

async function evaluateDuplicateIndex(newArtifact) {
    try {
        // Skip if no vessel ID
        if (!newArtifact.onboard_vessel_id) {
            console.log(`[evaluateDuplicateIndex] No vessel ID for artifact ${newArtifact._id}, setting duplication_index=null`);
            return null;
        }

        // Find the previous artifact from the same vessel within last 30 minutes
        const thirtyMinutesAgo = new Date(new Date(newArtifact.timestamp).getTime() - 30 * 60 * 1000);
        const prevArtifacts = await db.qmai.collection("analysis_results")
            .find(
                {
                    onboard_vessel_id: newArtifact.onboard_vessel_id,
                    timestamp: {
                        $lte: new Date(newArtifact.timestamp),
                        $gte: thirtyMinutesAgo
                    },
                },
                { sort: { timestamp: -1, _id: -1 } }
            ).toArray();

        const currArtifactIndex = prevArtifacts.findIndex((e) => e._id.toString() == newArtifact._id.toString());
        const prevArtifact = prevArtifacts[currArtifactIndex + 1];

        // If no previous artifact, set duplication_index=0
        if (!prevArtifact) {
            console.log(`[evaluateDuplicateIndex] No previous artifact for vessel ${newArtifact.onboard_vessel_id}, setting duplication_index=0`);
            return 0;
        }

        // Call Python single evaluation API
        const duplicateIndex = await evaluatorApi.evaluateSingle(prevArtifact, newArtifact);

        if (duplicateIndex !== null) {
            console.log(`[evaluateDuplicateIndex] Evaluated duplication_index=${duplicateIndex} for artifact ${newArtifact._id} against previous ${prevArtifact._id}`);
            return duplicateIndex;
        } else {
            console.log(`[evaluateDuplicateIndex] No evaluation result, setting duplication_index=null`);
            return null;
        }

    } catch (error) {
        console.error(`[evaluateDuplicateIndex] Error evaluating artifact ${newArtifact._id}:`, error);
        return null; // Default to 0 on error
    }
}

async function setLastProcessedArtifactId(artifacts) {
    if (!artifacts) throw new Error('[setLastProcessedArtifactId] artifacts are required');
    if (!artifacts.length) throw new Error('[setLastProcessedArtifactId] artifacts cannot be empty');

    const latestArtifact = artifacts.reduce((latest, current) => {
        return dateFromObjectId(current._id) > dateFromObjectId(latest._id) ? current : latest;
    }, artifacts[0]);

    console.log('[setLastProcessedArtifactId] latestArtifact.timestamp', latestArtifact.timestamp)
    console.log('[setLastProcessedArtifactId] latestArtifact._id', latestArtifact._id)

    await db.qmai.collection('processing_settings').updateOne({ name: processingSettingsLatestArtifactIdName }, { $set: { value: latestArtifact._id } });
}

function computeAisProximityConfidence({
    artifactBearing,
    aisBearing,
    bearingThreshold,
    artifactTimestamp,
    aisTimestamp,
    timeThreshold
}) {
    // --- Bearing score ---
    const diff = Math.abs(artifactBearing - aisBearing) % 360;
    const bDiff = diff > 180 ? 360 - diff : diff;
    let bearingScore = 1 - Math.min(bDiff, bearingThreshold) / bearingThreshold;

    // --- Time score ---
    const tDiff = Math.abs(artifactTimestamp - aisTimestamp);
    let timeScore = 1 - Math.min(tDiff, timeThreshold) / timeThreshold;

    // --- Combine scores (weighted average) ---
    const weightBearing = 0.5;
    const weightTime = 0.5;

    const confidence = bearingScore * weightBearing + timeScore * weightTime;

    return confidence;
}

const AIS_TIME_WINDOW_MS = 300 * 1000;
const AIS_BEARING_THRESHOLD_DEG = 10;

async function getAisInfo(artifact) {
    if (!artifact.onboard_vessel_id) {
        console.log(`[getAisInfo] (${artifact._id}) No vessel ID for artifact`);
        return null;
    }

    if (!artifact.true_bearing) {
        console.log(`[getAisInfo] (${artifact._id}) No true bearing for artifact`);
        return null;
    }

    const timestampISO = new Date(artifact.timestamp).toISOString()
    const isoSplit = timestampISO.split("-");
    const yearMonth = isoSplit[0] + "-" + isoSplit[1];

    const timeRange = [
        new Date(new Date(artifact.timestamp).getTime() - AIS_TIME_WINDOW_MS),
        new Date(new Date(artifact.timestamp).getTime() + AIS_TIME_WINDOW_MS)
    ]
    const bearingRange = getBearingRange(artifact.true_bearing, AIS_BEARING_THRESHOLD_DEG);

    console.log(`[getAisInfo] (${artifact._id}) timeRange`, artifact.timestamp, '=>', timeRange)
    console.log(`[getAisInfo] (${artifact._id}) bearingRange`, artifact.true_bearing, '=>', bearingRange)

    const query = {
        "metadata.onboard_vessel_id": artifact.onboard_vessel_id,
        timestamp: {
            $gte: timeRange[0],
            $lte: timeRange[1]
        },
        "details.message.portal_true_bearing_deg": {
            $gte: bearingRange[0],
            $lte: bearingRange[1]
        }
    }

    var aisInfos = await db.aisRaw.collection(yearMonth).find(query).toArray();

    if (!aisInfos.length) {
        console.log(`[getAisInfo] (${artifact._id}) No AIS info found`);
        return null;
    }

    const hasNonEmptyCategory = aisInfos.some(aisInfo => aisInfo.details.message.design_ais_ship_type_name !== '');

    if (hasNonEmptyCategory) {
        // filter for only non-empty categories
        aisInfos = aisInfos.filter(aisInfo => aisInfo.details.message.design_ais_ship_type_name !== '');
    }

    const aisInfosWithConfidence = aisInfos.map(aisInfo => ({
        aisInfo,
        confidence: computeAisProximityConfidence({
            artifactBearing: artifact.true_bearing,
            aisBearing: aisInfo.details.message.portal_true_bearing_deg,
            bearingThreshold: AIS_BEARING_THRESHOLD_DEG,
            artifactTimestamp: artifact.timestamp,
            aisTimestamp: aisInfo.timestamp,
            timeThreshold: AIS_TIME_WINDOW_MS
        })
    }));

    const aisInfoWithHighestConfidence = aisInfosWithConfidence.sort((a, b) => b.confidence - a.confidence)[0];

    console.log(`[getAisInfo] (${artifact._id}) aisInfo`, aisInfoWithHighestConfidence.aisInfo)
    console.log(`[getAisInfo] (${artifact._id}) confidence`, aisInfoWithHighestConfidence.confidence)

    return {
        aisInfo: {
            _id: aisInfoWithHighestConfidence.aisInfo._id,
            ...aisInfoWithHighestConfidence.aisInfo.details.message
        },
        confidence: aisInfoWithHighestConfidence.confidence
    };
}

async function detectNewArtifacts() {
    try {
        const latestArtifactId = await getLastProcessedArtifactId();

        const artifacts = await db.qmai.collection('analysis_results').aggregate([
            // get the objects that have timestamp greater than latestTimestamp
            {
                $match: {
                    _id: { $gt: latestArtifactId },
                    vessel_presence: true,
                    // super_category: {$ne: null},
                    location: { $ne: null }
                }
            },
        ]).toArray();

        /**
        //fetch some random artifacts where category is not null and sub_category is not null and flags is not null 10 artifacts
        const artifacts = await db.qmai.collection('analysis_results').aggregate([
            { $match: { $or: [{ category: { $ne: null } }, { sub_category: { $ne: null } }, { country_flag: { $ne: null } }] } },            { $sample: { size: 10 } }
        ]).toArray();

        const artifacts = await db.qmai.collection('analysis_results').aggregate([
        { $match: { country_flag: { $nin: [null] }, timestamp: { $gte: new Date('2025-02-10') } } },
        ]).toArray();
        console.log(`[updateStatistics] artifacts length: ${artifacts.length}`);
        */

        if (!artifacts.length) {
            console.info('[detectNewArtifacts] No new artifacts are found');
            return;
        }

        if (artifacts.length > 100) {
            postLogToSlack({
                severity: 'warning',
                message: `Large number of artifacts detected\nTotal length: ${artifacts.length}`,
                stack: new Error().stack
            });
        }

        console.log('[detectNewArtifacts] artifacts', artifacts)

        await preprocessArtifacts(artifacts);
        console.log('[detectNewArtifacts] artifact updated');

        artifacts.forEach((artifact) => {
            io.emit('artifactDetected', artifact);
        });

        try {
            await processNotificationAlerts({ artifacts });
        } catch (err) {
            console.error('FATAL: [detectNewArtifacts] error occurred while processing notification alerts', err);
        }

        try {
            await processSeaVisionRequests(artifacts);
        } catch (err) {
            console.error('FATAL: [detectNewArtifacts] error occurred while processing SeaVision requests', err);
        }

        await setLastProcessedArtifactId(artifacts);
    } catch (err) {
        console.error('FATAL: [detectNewArtifacts] error occurred', err);
        postLogToSlack({
            severity: 'fatal',
            message: 'Error post-processing artifact detections',
            stack: err.stack
        });
    } finally {
        setTimeout(detectNewArtifacts, detectingNewArtifactsPeriodic);
    }
}

async function preprocessArtifacts(artifacts) {
    const updates = await Promise.all(artifacts.map(async (artifact) => {
        if (!artifact.image_path || !artifact.bucket_name) {
            return null
        }

        let res = undefined;

        const thumbnail_image_path = await buildThumbnailImage(artifact.bucket_name, artifact.aws_region, artifact.image_path, artifact.unit_id);
        if (thumbnail_image_path) {
            artifact.thumbnail_image_path = thumbnail_image_path;
            res = { thumbnail_image_path };
        }

        const videoPath = artifact.image_path.replace('/image/', '/video/').replace('.jpg', '.mp4');
        const params = {
            Bucket: artifact.bucket_name,
            Key: videoPath,
        };

        try {
            await s3.headObject(params).promise();
            console.info(`[updateVideoUrlObjectsDetected] Video is found`);
            artifact.video_path = videoPath;

            (res ||= {}).video_path = videoPath;
        } catch (err) {
            if (err.code !== 'NotFound') {
                console.error(`[updateVideoUrlObjectsDetected] Error while checking video: ${err}`);
            }
        }

        try {
            const duplicateIndex = await evaluateDuplicateIndex(artifact);
            (res ||= {})['portal.duplication_index'] = duplicateIndex;
        } catch (err) {
            console.error(`[preprocessArtifacts] Error evaluating duplicate index: ${err}`);
        }

        try {
            const aisInfo = await getAisInfo(artifact);
            (res ||= {})['portal.ais_info'] = !aisInfo ? null : { data: aisInfo.aisInfo, proximity_confidence: aisInfo.confidence };
        } catch (err) {
            console.error(`[preprocessArtifacts] Error getting AIS info: ${err}`);
        }

        return res ? {
            updateOne: {
                filter: { _id: artifact._id },
                update: { $set: res }
            }
        } : null;
    }));

    const validUpdates = updates.filter(update => update !== null);

    if (validUpdates.length > 0) {
        const result = await db.qmai.collection('analysis_results').bulkWrite(validUpdates);
        console.log(`[updateVideoUrlObjectsDetected] Updated documents: ${JSON.stringify(result)}`);
    }
}

detectNewArtifacts();