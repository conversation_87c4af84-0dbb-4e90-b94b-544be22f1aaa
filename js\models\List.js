const mongoose = require("mongoose");
const db = require("../modules/db");

const listSchema = new mongoose.Schema({
    owner_id: { type: mongoose.Schema.Types.ObjectId, ref: "User", required: true, index: true },
    name: { type: String, required: true, unique: true, trim: true }, // globally unique
    shared_with_organization: { type: Boolean, required: true, default: false },
    is_deleted: { type: Boolean, required: true, default: false },
    created_at: { type: Date, required: true, default: () => new Date() },
    updated_at: { type: Date, required: true, default: () => new Date() },
});

// Secondary indexes for common queries
listSchema.index({ owner_id: 1, created_at: -1 });

const List = db.qm.model("list", listSchema);

module.exports = List;
