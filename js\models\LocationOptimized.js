// const { listThings } = require("../modules/awsIot")
const mongoose = require('mongoose');
const db = require("../modules/db");

let OptimizedLocations = {}

const getLocationOptimizedCollection = async (month) => {
    const collection = month

    if (OptimizedLocations[collection]) return OptimizedLocations[collection]

    const OptimizedLocationSchema = new mongoose.Schema({
        location: {
            type: {
                type: String,
                enum: ['Point'],
                required: true
            },
            coordinates: {
                type: [Number],
                required: true
            }
        },
        groundSpeed: { type: Number, required: true },
        isStationary: { type: <PERSON><PERSON><PERSON>, required: true },
        headingMotion: { type: Number, required: true },
        accuracyHeading: { type: Number, required: true },
        metadata: { type: Object, required: true },
        // details: { type: Object, required: true },
        timestamp: { type: Date, required: true }
    }, { minimize: false, timeseries: { timeField: 'timestamp', metaField: 'metadata', granularity: 'seconds' } });

    OptimizedLocationSchema.index({ timestamp: 1, 'metadata.onboardVesselId': 1 })
    OptimizedLocationSchema.index({ 'metadata.onboardVesselId': 1 })

    const model = db.locationsOptimized.model(collection, OptimizedLocationSchema, collection);
    await model.createCollection()

    OptimizedLocations[collection] = model

    return model
}

module.exports = {
    getLocationOptimizedCollection
};


// db.getCollectionNames().forEach(function(c) {
//     if (c.endsWith("_location")) {
//       db[c].drop();
//     }
//   });