const mongoose = require("mongoose");
const db = require("../modules/db");

const listArtifactSchema = new mongoose.Schema({
    list_id: { type: mongoose.Schema.Types.ObjectId, ref: "List", required: true, index: true },
    artifact_id: { type: mongoose.Schema.Types.ObjectId, ref: "Artifact", required: true, index: true },
    added_by: { type: mongoose.Schema.Types.ObjectId, ref: "User", required: true },
    created_at: { type: Date, required: true, default: () => new Date() },
});

// Ensure each artifact appears once per list
listArtifactSchema.index({ list_id: 1, artifact_id: 1 }, { unique: true });
listArtifactSchema.index({ list_id: 1, created_at: -1 });

const ListArtifact = db.qm.model("list_artifact", listArtifactSchema);

module.exports = ListArtifact;
