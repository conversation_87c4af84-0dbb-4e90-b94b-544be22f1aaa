// // repro-timeseries-race.js

require('dotenv').config();
const { getLocationCollection } = require('../models/VesselLocation');
const db = require('../modules/db');

db.qmLocations.once('open', async () => {
    for (let i = 0; i < 5; i++) {
        getLocationCollection(`QSXTEST9${i}`).then(col => {
            col.create({
                latitude: 1,
                longitude: 1,
                groundSpeed: 1,
                isStationary: false,
                timestamp: new Date()
            }).then(res => {
                console.log('res', res)
            })
        })
        getLocationCollection(`QSXTEST5${i}`).then(col => {
            col.create({
                latitude: 1,
                longitude: 1,
                groundSpeed: 1,
                isStationary: false,
                timestamp: new Date()
            }).then(res => {
                console.log('res', res)
            })
        })
        getLocationCollection(`QSXTEST5${i}`).then(col => {
            col.create({
                latitude: 1,
                longitude: 1,
                groundSpeed: 1,
                isStationary: false,
                timestamp: new Date()
            }).then(res => {
                console.log('res', res)
            })
        })
    }
})