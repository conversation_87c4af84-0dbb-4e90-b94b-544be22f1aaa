const { sendEmail } = require('../modules/awsSes');
require('dotenv').config();

async function testWorkMailSES() {
    console.log('🧪 Testing AWS WorkMail + SES Integration\n');
    
    const fromEmail = process.env.AWS_SES_FROM_EMAIL;
    const testEmail = '<EMAIL>'; // Use your verified email for testing
    
    console.log(`📧 From: ${fromEmail}`);
    console.log(`📧 To: ${testEmail}`);
    console.log('📧 Subject: WorkMail SES Test');
    
    const htmlContent = `
        <html>
            <body>
                <h2>🎉 WorkMail + SES Test Successful!</h2>
                <p>This email was sent from AWS WorkMail via SES.</p>
                <p><strong>From:</strong> ${fromEmail}</p>
                <p><strong>Time:</strong> ${new Date().toISOString()}</p>
                <p><strong>Environment:</strong> ${process.env.NODE_ENV || 'development'}</p>
                
                <hr>
                <p style="color: #666; font-size: 12px;">
                    This is a test email to verify WorkMail + SES integration.
                </p>
            </body>
        </html>
    `;
    
    try {
        console.log('\n⏳ Sending test email...');
        
        const result = await sendEmail(
            testEmail,
            '🧪 WorkMail SES Integration Test',
            htmlContent
        );
        
        console.log('✅ Email sent successfully!');
        console.log(`📋 Message ID: ${result.MessageId}`);
        console.log('\n💡 Check your inbox for the test email.');
        
    } catch (error) {
        console.error('❌ Failed to send test email:');
        console.error(`   Error: ${error.message}`);
        
        if (error.name === 'MessageRejected') {
            console.log('\n💡 Possible issues:');
            console.log('   - WorkMail user not created yet');
            console.log('   - Domain not fully verified');
            console.log('   - SES still in sandbox mode');
        }
        
        if (error.name === 'AccessDenied') {
            console.log('\n💡 Permission issues:');
            console.log('   - IAM user needs ses:SendEmail permission');
            console.log('   - WorkMail domain not properly configured');
        }
    }
}

// Run the test
testWorkMailSES();
