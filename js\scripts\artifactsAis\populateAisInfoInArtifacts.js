require('dotenv').config();
const db = require("../../modules/db");
const { getTerminalConfirmation, getBearingRange } = require('../../utils/functions');

const getDailyTimeRange = (start, end) => {
    const ranges = []
    let current = new Date(start)

    while (current < end) {
        const next = new Date(current)
        next.setUTCDate(next.getUTCDate() + 1)
        next.setUTCHours(0, 0, 0, 0)

        // range ends either at `next` or at the final `end` timestamp
        const rangeEnd = next < end ? next : end

        ranges.push([new Date(current), new Date(rangeEnd)])
        current = new Date(next)
    }

    return ranges
}


function computeAisProximityConfidence({
    artifactBearing,
    aisBearing,
    bearingThreshold,
    artifactTimestamp,
    aisTimestamp,
    timeThreshold
}) {
    // --- Bearing score ---
    const diff = Math.abs(artifactBearing - aisBearing) % 360;
    const bDiff = diff > 180 ? 360 - diff : diff;
    let bearingScore = 1 - Math.min(bDiff, bearingThreshold) / bearingThreshold;

    // --- Time score ---
    const tDiff = Math.abs(artifactTimestamp - aisTimestamp);
    let timeScore = 1 - Math.min(tDiff, timeThreshold) / timeThreshold;

    // --- Combine scores (weighted average) ---
    const weightBearing = 0.5;
    const weightTime = 0.5;

    const confidence = bearingScore * weightBearing + timeScore * weightTime;

    return confidence;
}

const AIS_TIME_WINDOW_MS = 300 * 1000;
const AIS_BEARING_THRESHOLD_DEG = 10;

async function getAisInfo(artifact) {
    if (!artifact.onboard_vessel_id) {
        // console.log(`[getAisInfo] (${artifact._id}) No vessel ID for artifact`);
        return null;
    }

    if (!artifact.true_bearing) {
        // console.log(`[getAisInfo] (${artifact._id}) No true bearing for artifact`);
        return null;
    }

    const timestampISO = new Date(artifact.timestamp).toISOString()
    const isoSplit = timestampISO.split("-");
    const yearMonth = isoSplit[0] + "-" + isoSplit[1];

    const timeRange = [
        new Date(new Date(artifact.timestamp).getTime() - AIS_TIME_WINDOW_MS),
        new Date(new Date(artifact.timestamp).getTime() + AIS_TIME_WINDOW_MS)
    ]
    const bearingRange = getBearingRange(artifact.true_bearing, AIS_BEARING_THRESHOLD_DEG);

    // console.log(`[getAisInfo] (${artifact._id}) timeRange`, artifact.timestamp, '=>', timeRange)
    // console.log(`[getAisInfo] (${artifact._id}) bearingRange`, artifact.true_bearing, '=>', bearingRange)

    const query = {
        "metadata.onboard_vessel_id": artifact.onboard_vessel_id,
        timestamp: {
            $gte: timeRange[0],
            $lte: timeRange[1]
        },
        "details.message.portal_true_bearing_deg": {
            $gte: bearingRange[0],
            $lte: bearingRange[1]
        }
    }

    var aisInfos = await db.aisRaw.collection(yearMonth).find(query).toArray();

    if (!aisInfos.length) {
        // console.log(`[getAisInfo] (${artifact._id}) No AIS info found`);
        return null;
    }

    const hasNonEmptyCategory = aisInfos.some(aisInfo => aisInfo.details.message.design_ais_ship_type_name !== '');

    if (hasNonEmptyCategory) {
        // filter for only non-empty categories
        aisInfos = aisInfos.filter(aisInfo => aisInfo.details.message.design_ais_ship_type_name !== '');
    }

    const aisInfosWithConfidence = aisInfos.map(aisInfo => ({
        aisInfo,
        confidence: computeAisProximityConfidence({
            artifactBearing: artifact.true_bearing,
            aisBearing: aisInfo.details.message.portal_true_bearing_deg,
            bearingThreshold: AIS_BEARING_THRESHOLD_DEG,
            artifactTimestamp: artifact.timestamp,
            aisTimestamp: aisInfo.timestamp,
            timeThreshold: AIS_TIME_WINDOW_MS
        })
    }));

    const aisInfoWithHighestConfidence = aisInfosWithConfidence.sort((a, b) => b.confidence - a.confidence)[0];

    // console.log(`[getAisInfo] (${artifact._id}) aisInfo`, aisInfoWithHighestConfidence.aisInfo)
    // console.log(`[getAisInfo] (${artifact._id}) confidence`, aisInfoWithHighestConfidence.confidence)

    return {
        aisInfo: {
            _id: aisInfoWithHighestConfidence.aisInfo._id,
            ...aisInfoWithHighestConfidence.aisInfo.details.message
        },
        confidence: aisInfoWithHighestConfidence.confidence
    };
}

async function run() {
    const startTimestamp = new Date('2025-09-01T00:00:00.000Z')
    const endTimestamp = new Date('2025-10-01T00:00:00.000Z')

    const dayRange = getDailyTimeRange(startTimestamp, endTimestamp)


    console.log('dayRange', dayRange)

    const confirmation = await getTerminalConfirmation(`Processing for above ranges, continue? (y/n): `)
    if (!confirmation) {
        console.log('User did not confirm, exiting...');
        process.exit(0);
    }

    for (const range of dayRange) {
        console.log('processing artifacts for range', range)

        const artifacts = await db.qmai.collection('analysis_results').find({
            timestamp: { $gte: range[0], $lt: range[1] },
            // 'portal.ais_info': { $exists: false }
        }, {
            projection: {
                _id: 1,
                timestamp: 1,
                onboard_vessel_id: 1,
                true_bearing: 1
            }
        }).toArray()
        console.log('artifacts', artifacts.length)

        if (artifacts.length === 0) {
            console.log('No artifacts found for range', range)
            continue
        }

        const updateQueries = []


        await Promise.all(artifacts.map(async (artifact) => {
            const aisInfo = await getAisInfo(artifact)
            updateQueries.push({
                updateOne: {
                    filter: { _id: artifact._id },
                    update: { $set: { 'portal.ais_info': aisInfo ? { data: aisInfo.aisInfo, proximity_confidence: aisInfo.confidence } : null } }
                }
            })
        }))

        // for (const artifact of artifacts) {
        //     const aisInfo = await getAisInfo(artifact)
        //     updateQueries.push({
        //         updateOne: {
        //             filter: { _id: artifact._id },
        //             update: { $set: { 'portal.ais_info': aisInfo } }
        //         }
        //     })
        // }

        console.log('updateQueries', updateQueries.length)
        // console.log('updateQueries', JSON.stringify(updateQueries))
        console.log('updateQueries not null', updateQueries.filter(o => o.updateOne.update['$set']['portal.ais_info']).length)

        await db.qmai.collection('analysis_results').bulkWrite(updateQueries)

        console.log('Successfully updated artifacts for range', range)
    }
}

Promise.all([
    new Promise((resolve, reject) => {
        db.aisRaw.once('open', resolve);
    }),
    new Promise((resolve, reject) => {
        db.qmai.once('open', resolve);
    }),
]).then(async () => {
    await run();
    console.log('Success')
    process.exit(0)
}).catch(err => {
    console.error('Failed to establish database connections:', err);
    process.exit(1);
});