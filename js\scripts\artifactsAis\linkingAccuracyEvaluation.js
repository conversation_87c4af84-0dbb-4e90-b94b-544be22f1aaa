require('dotenv').config();
const { s3 } = require('../../modules/awsS3');
const db = require("../../modules/db");
const path = require('path');
const fs = require('fs');

const startDate = new Date('2025-09-01T00:00:00.000Z')
const endDate = new Date('2025-09-30T23:59:59.999Z')

var baseQuery = {
    timestamp: { $gte: startDate, $lte: endDate },
    location: { $ne: null },
    vessel_presence: true,
    super_category: { $ne: null }
}

var aisOnlyQuery = {
    ...baseQuery,
    'portal.ais_info': { $ne: null }
}

// {
//     "uniqueValues": [
//       "Towing exceeds 200m or wider than 25m",
//       "RR Resolution No.18",
//       "Cargo ship hazard cat D",
//       "Cargo ship",
//       "Pleasure",
//       "Sailing",
//       "Tanker hazard cat C",
//       "Tug",
//       "SAR",
//       "Engaged in military operations",
//       "Other hazard cat B",
//       "High speed craft",
//       "Tanker hazard cat D",
//       "Tanker (no additional information)",
//       "Cargo ship hazard cat B",
//       "Cargo ship carrying dangerous goods",
//       "Towing",
//       "Passenger ship (no additional information)",
//       "Cargo ship (no additional information)",
//       "Tanker carrying dangerous goods",
//       "Tanker hazard cat B",
//       "Cargo ship hazard cat C",
//       "Law enforcement",
//       "Other",
//       "Other (no additional information)",
//       "Fishing",
//       "Tanker",
//       "",
//       "Pilot vessel",
//       "Engaged in dredging or underwater operations",
//       null,
//       "Passenger ship"
//     ]
//   }

// references: 
// https://servicedocs-sm.kpler.com/ais-fundamentals/ship-type-mappings/
// https://qmai.atlassian.net/wiki/spaces/ML/pages/273022977/Artifact+Analysis+Results


const superCategoryMap = {
    Tankers: ["Tanker hazard cat D", "Tanker", "Tanker (no additional information)", "Tanker carrying dangerous goods", "Tanker hazard cat B", "Tanker hazard cat C"],
    Passenger: ["Passenger ship", "Passenger ship (no additional information)"],
    Cargo: ["Cargo ship (no additional information)", "Cargo ship hazard cat B", "Cargo ship", "Cargo ship hazard cat D", "Cargo ship carrying dangerous goods", "Cargo ship hazard cat C"],
    Military: ["Law enforcement", "Engaged in military operations"],
    Fishing: ["Fishing"],
    Tugs: ["Tug", "Towing", "Towing exceeds 200m or wider than 25m"],
    "Pleasure Craft": ["High speed craft", "Pleasure"],
    Tanker: [],
    "Special Craft": ["RR Resolution No.18", "Pilot vessel", "Engaged in dredging or underwater operations", "SAR"],
}

// Where do the followings clasify?
// Sailing, SAR, Pilot vessel, Engaged in dredging or underwater operations

// const superCategoryMap = {
//     Fishing: ['Fishing'],
//     Tankers: [
//       'Tanker', 
//       'Tanker (no additional information)', 
//       'Tanker hazard cat B', 
//       'Tanker carrying dangerous goods'
//     ],
//     Cargo: [
//       'Cargo ship', 
//       'Cargo ship (no additional information)', 
//       'Cargo ship hazard cat C', 
//       'Cargo ship hazard cat D', 
//       'Cargo ship carrying dangerous goods'
//     ],
//     'Special Craft': [],
//     Military: ['Law enforcement'],
//     Passenger: ['Passenger ship'],
//     Tugs: [],
//     'Pleasure Craft': ['High speed craft']
// }

const getVesselFirstAisMessage = async (vesselId) => {
    const collections = ['2025-08', '2025-09', '2025-10']
    for (const collection of collections) {
        const message = await db.aisRaw.collection(collection).findOne({
            'metadata.onboard_vessel_id': vesselId
        })
        if (message) {
            return message;
        }
    }
    return null
}

const getSDROnlyVessels = async () => {
    const vessels = await db.qmShared.collection('vessels').find().toArray()

    const sdrOnlyVessels = (await Promise.all(vessels.map(async (vessel) => {
        const firstMessage = await getVesselFirstAisMessage(vessel._id)
        if (firstMessage) {
            vessel.sdr_mount_timestamp = firstMessage.timestamp
        }
        return vessel;
    }))).filter(vessel => vessel.sdr_mount_timestamp)

    return sdrOnlyVessels
}

function getContentTypeFromFileExtension(fileExtension) {
    const fileType = fileExtension;
    if (
        fileType === "jpg" ||
        fileType === "jpeg" ||
        fileType === "png" ||
        fileType === "gif" ||
        fileType === "bmp" ||
        fileType === "tiff" ||
        fileType === "ico" ||
        fileType === "webp"
    ) {
        return `image/${fileType}`;
    } else if (
        fileType === "mp4" ||
        fileType === "mov" ||
        fileType === "avi" ||
        fileType === "wmv" ||
        fileType === "flv" ||
        fileType === "mkv" ||
        fileType === "webm"
    ) {
        return `video/${fileType}`;
    }
    return "application/octet-stream";
}

const populateImageSignedUrl = (artifacts) => {
    const updates = artifacts.map((artifact) => {
        const s3Region = artifact.aws_region || "us-east-1";
        s3.config.update({ region: s3Region, signatureVersion: "v4" });

        const fileType = artifact.image_path.split(".").pop();
        if (!fileType) throw new Error("fileType not found");
        const ResponseContentType = getContentTypeFromFileExtension(fileType);

        const url = s3.getSignedUrl("getObject", {
            Bucket: artifact.bucket_name,
            Key: artifact.image_path,
            Expires: 7 * 24 * 60 * 60, // 7 days
            ResponseContentType,
        });

        artifact.image_signed_url = url

        return artifact
    })

    return updates
}

function toRadians(deg) {
    return deg * Math.PI / 180;
}

function haversineDistance(lat1, lon1, lat2, lon2) {
    const R = 6371000; // Earth radius in meters
    const φ1 = toRadians(lat1);
    const φ2 = toRadians(lat2);
    const Δφ = toRadians(lat2 - lat1);
    const Δλ = toRadians(lon2 - lon1);

    const a = Math.sin(Δφ / 2) ** 2 +
        Math.cos(φ1) * Math.cos(φ2) *
        Math.sin(Δλ / 2) ** 2;

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c; // distance in meters
}

const generateHtml = (artifacts, {
    matchedCount,
    mismatchedCount,
    totalCount,
    linkedCount
}) => {
    return `
    <html>
      <head>
        <style>
          pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 6px;
            max-height: 400px;
            overflow: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
          }
          details {
            margin-top: 10px;
            max-width: 100%;
          }
          summary {
            cursor: pointer;
            font-weight: bold;
            padding: 5px 0;
          }
          .row {
            display: flex;
            flex-direction: row;
            gap: 20px;
            flex-wrap: wrap;
          }
          .col {
            flex: 1;
            min-width: 200px;
            max-width: 100%;
          }
          .img-col {
            max-width: 600px;   /* fix big gap issue */
          }
          .kpi-col {
            flex: 1;
            min-width: 180px;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 8px;
            background: #fafafa;
          }
          .kpi-row {
            margin: 5px 0;
          }
          img {
            max-width: 100%;
            height: auto;
            border-radius: 6px;
          }
        </style>
      </head>
      <body>
      <div>
        <div class="col">
          <h3>Date Range (UTC): ${startDate.toISOString()} to ${endDate.toISOString()}</h3>
          <h3>Base query:</h3>
          <pre>${JSON.stringify(baseQuery, null, 2)}</pre>
          <h3>Total Artifact Detections: ${totalCount}</h3>
          <h3>AIS Linked Artifacts: ${linkedCount}</h3>
          <h3>AIS Matched Artifacts (based on super category): ${matchedCount}</h3>
          <h3>AIS Mismatched Artifacts (based on super category): ${mismatchedCount}</h3>
          <h3>Linking score: ${(linkedCount / totalCount * 100).toFixed(2)}%</h3>
          <h3>Potential matching score: ${(matchedCount / totalCount * 100).toFixed(2)}%</h3>
          <h3>Mapping between AIS and Artifact Super Category:</h3>
          <pre>${JSON.stringify(superCategoryMap, null, 2)}</pre>
          <h3>Below are all the mismatched artifacts:</h3>
        </div>
      </div>
        <div style="display: flex; flex-direction: column; gap: 20px;">
          ${artifacts.map((artifact) => {
        // Each KPI column is an array of rows
        const kpis = [
            [
                { key: "Artifact AI Detection Confidence", value: artifact.det_conf },
                { key: "AIS Proximity Confidence", value: artifact.portal.ais_info.proximity_confidence }
            ],
            [
                { key: "Artifact Super Category", value: artifact.super_category },
                { key: "AIS Category", value: artifact.portal.ais_info.data.design_ais_ship_type_name || "N/A" }
            ],
            [
                { key: "Artifact Detection Timestamp (UTC)", value: new Date(artifact.timestamp).toISOString() },
                { key: "AIS Message Timestamp (UTC)", value: new Date(artifact.portal.ais_info.data.timestamp).toISOString() },
                { key: "Time Difference (seconds)", value: Math.abs(new Date(artifact.timestamp).getTime() - new Date(artifact.portal.ais_info.data.timestamp).getTime()) / 1000 }
            ],
            [
                { key: "Artifact True Bearing Angle", value: artifact.true_bearing },
                { key: "AIS True Bearing Angle", value: artifact.portal.ais_info.data.portal_true_bearing_deg },
                { key: "Bearing Difference (degrees)", value: Math.abs(artifact.true_bearing - artifact.portal.ais_info.data.portal_true_bearing_deg) }
            ],
            [
                { key: "Host Vessel Location", value: `${artifact.portal.ais_info.data.host_location_latitude}, ${artifact.portal.ais_info.data.host_location_longitude}` },
                { key: "AIS Vessel Location", value: `${artifact.portal.ais_info.data.nav_latitude}, ${artifact.portal.ais_info.data.nav_longitude}` },
                { key: "Distance Difference (meters)", value: Math.abs(haversineDistance(artifact.portal.ais_info.data.host_location_latitude, artifact.portal.ais_info.data.host_location_longitude, artifact.portal.ais_info.data.nav_latitude, artifact.portal.ais_info.data.nav_longitude)) }
            ],
        ];

        const aisInfo = artifact.portal.ais_info.data;
        const artifactInfo = { ...artifact };
        delete artifactInfo.portal.ais_info;
        delete artifactInfo.image_signed_url;

        return `
              <div class="row">
                <div class="col img-col">
                  <img src="${artifact.image_signed_url}" width="600"/>
                </div>
                <div class="col">
                  <div class="row">
                    ${kpis.map((col) => {
            return `
                        <div class="kpi-col">
                          ${col.map((row) => `
                            <div class="kpi-row"><b>${row.key}:</b> ${row.value}</div>
                          `).join("")}
                        </div>
                      `
        }).join("")}
                  </div>
                  <div class="row">
                    <div class="col">
                      <details>
                        <summary>Artifact Info</summary>
                        <pre>${JSON.stringify(artifactInfo, null, 2)}</pre>
                      </details>
                    </div>
                    <div class="col">
                      <details>
                        <summary>AIS Info</summary>
                        <pre>${JSON.stringify(aisInfo, null, 2)}</pre>
                      </details>
                    </div>
                  </div>
                </div>
              </div>
            `
    }).join('')}
        </div>
      </body>
    </html>
    `
}

const getSDROnlyArtifacts = async () => {
    const sdrOnlyVessels = await getSDROnlyVessels()
    // fs.writeFileSync(path.join(__dirname, '../temp/sdr_only_vessels.json'), JSON.stringify(sdrOnlyVessels, null, 2))
    const sdrVesselMountMap = sdrOnlyVessels.reduce((acc, vessel) => {
        acc[vessel._id] = vessel.sdr_mount_timestamp
        return acc
    }, {})

    aisOnlyQuery.onboard_vessel_id = { $in: sdrOnlyVessels.map(vessel => vessel._id) }
    baseQuery.onboard_vessel_id = { $in: sdrOnlyVessels.map(vessel => vessel._id) }

    const totalArtifacts = await db.qmai.collection('analysis_results').find(baseQuery, { projection: { _id: 1, timestamp: 1, onboard_vessel_id: 1 } }).toArray()
    const artifacts = await db.qmai.collection('analysis_results').find(aisOnlyQuery).toArray()

    const sdrMountFilter = (artifact) => {
        if (!sdrVesselMountMap[artifact.onboard_vessel_id]) {
            return false
        }
        return new Date(artifact.timestamp).getTime() >= new Date(sdrVesselMountMap[artifact.onboard_vessel_id]).getTime()
    }

    const totalArtifactsFiltered = totalArtifacts.filter(sdrMountFilter)
    const artifactsFiltered = artifacts.filter(sdrMountFilter)



    return {
        totalCount: totalArtifactsFiltered.length,
        artifacts: artifactsFiltered
    }
}

async function run() {
    // console.log('sdrOnlyVessels', sdrOnlyVessels.length)
    // console.log('sdrOnlyVessels', sdrOnlyVessels)
    // return;

    const { totalCount, artifacts } = await getSDROnlyArtifacts()
    console.log('totalCount', totalCount)
    console.log('artifacts', artifacts.length)

    // fs.writeFileSync(path.join(__dirname, '../temp/sdr_only_artifacts.json'), JSON.stringify(artifacts, null, 2))

    // const possibleValues = {
    //     artifactSuperCategories: artifacts.reduce((acc, artifact) => {
    //         const cat = artifact.super_category
    //         if (!acc[cat]) {
    //             acc[cat] = 0
    //         }
    //         acc[cat]++
    //         return acc
    //     }, {}),
    //     aisSuperCategories: artifacts.reduce((acc, artifact) => {
    //         const cat = artifact.portal.ais_info.data.design_ais_ship_type_name
    //         if (!acc[cat]) {
    //             acc[cat] = 0
    //         }
    //         acc[cat]++
    //         return acc
    //     }, {}),
    // }

    // console.log('possibleValues', possibleValues)

    const matchedArtifacts = artifacts.filter(artifact => {
        const cat = artifact.super_category
        return superCategoryMap[cat].includes(artifact.portal.ais_info.data.design_ais_ship_type_name)
    })

    console.log('matchedArtifacts', matchedArtifacts.length)

    const mismatchedArtifactsAis = artifacts.filter(artifact => {
        const cat = artifact.super_category
        return !superCategoryMap[cat].includes(artifact.portal.ais_info.data.design_ais_ship_type_name)
    })

    console.log('mismatchedArtifactsAis', mismatchedArtifactsAis.length)

    const updates = await populateImageSignedUrl(mismatchedArtifactsAis)

    const htmlFile = generateHtml(updates, {
        totalCount,
        linkedCount: artifacts.length,
        matchedCount: matchedArtifacts.length,
        mismatchedCount: mismatchedArtifactsAis.length
    })
    const filePath = path.join(__dirname, '../temp/eval-4_mismatched-artifacts-ais.html')
    fs.writeFileSync(filePath, htmlFile)

    console.log(`HTML file generated at ${filePath}`)
}

Promise.all([
    new Promise((resolve, reject) => {
        db.qmai.once('open', resolve);
        db.qmai.on('error', reject);
    })
]).then(() => {
    setTimeout(async () => {
        await run()
        process.exit(0)
    }, 1000);
}).catch(err => {
    console.error('Failed to establish database connections:', err);
    process.exit(1);
});