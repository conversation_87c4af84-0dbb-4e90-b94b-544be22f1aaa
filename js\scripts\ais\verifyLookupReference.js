require('dotenv').config();
const db = require("../../modules/db");

async function run() {
    const aisLookups = await db.lookups.collection('ais_mmsi_lookup').find({}, { projection: { _id: 1, collection: 1, last_message_id: 1 } }).toArray()
    console.log('aisLookups', aisLookups.length)

    const aisCollections = aisLookups.reduce((acc, aisLookup) => {
        if (!acc[aisLookup.collection]) {
            acc[aisLookup.collection] = []
        }
        acc[aisLookup.collection].push(aisLookup.last_message_id)
        return acc
    }, {})

    const allAis = (await Promise.all(Object.keys(aisCollections).map(async (collection) => {
        return await db.aisRaw.collection(collection).find({ _id: { $in: aisCollections[collection] } }, { projection: { _id: 1 } }).toArray()
    }))).flat()

    console.log('allAis', allAis.length)

    const missingRefs = aisLookups.filter(aisLookup => !allAis.find(ais => ais._id.toString() === aisLookup.last_message_id.toString()))
    console.log('missingRefs', missingRefs.length)
}

Promise.all([
    new Promise((resolve, reject) => {
        db.lookups.once('open', resolve);
        db.lookups.on('error', reject);
    }),
    new Promise((resolve, reject) => {
        db.aisRaw.once('open', resolve);
        db.aisRaw.on('error', reject);
    })
]).then(() => {
    setTimeout(() => {
        run()
    }, 1000);
}).catch(err => {
    console.error('Failed to establish database connections:', err);
    process.exit(1);
});