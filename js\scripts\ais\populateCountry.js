require('dotenv').config();
const db = require("../../modules/db");
const { getCountryByMmsi } = require('../../utils/functions');

const getDailyTimeRange = (start, end) => {
    const ranges = []
    let current = new Date(start)

    while (current < end) {
        const next = new Date(current)
        next.setUTCDate(next.getUTCDate() + 1)
        next.setUTCHours(0, 0, 0, 0)

        // range ends either at `next` or at the final `end` timestamp
        const rangeEnd = next < end ? next : end

        ranges.push([new Date(current), new Date(rangeEnd)])
        current = new Date(next)
    }

    return ranges
}


async function run() {
    const startTimestamp = new Date('2025-09-01T00:00:00.000Z')
    const endTimestamp = new Date('2025-10-01T00:00:00.000Z')

    const dayRange = getDailyTimeRange(startTimestamp, endTimestamp)

    const collection = db.aisRaw.collection('2025-09')

    for (const range of dayRange) {
        console.log('processing for range', range)
        const aisData = await collection.find({
            timestamp: {
                $gte: range[0],
                $lt: range[1]
            }
        }).toArray()

        console.log('aisData', aisData.length)

        if (aisData.length === 0) {
            console.log('no ais data found for range', range)
            continue
        }

        const updatedAisData = aisData.map(ais => {
            ais.details.message.portal_registry_country = getCountryByMmsi(ais.metadata.mmsi)
            return ais;
        })

        console.log('updatedAisData', updatedAisData.length)

        console.log('deleting records for range', startTimestamp, endTimestamp)
        await collection.deleteMany({
            timestamp: {
                $gte: startTimestamp,
                $lt: endTimestamp
            }
        })

        console.log('inserting records for range', startTimestamp, endTimestamp)
        await collection.insertMany(updatedAisData)
    }


    // console.log('updateQueries', JSON.stringify(updateQueries, null, 2))
}

Promise.all([
    new Promise((resolve, reject) => {
        db.aisRaw.once('open', resolve);
        db.aisRaw.on('error', reject);
    }),
]).then(() => {
    setTimeout(async () => {
        await run()
        console.log('Success')
        process.exit(0)
    }, 1000);
}).catch(err => {
    console.error('Failed to establish database connections:', err);
    process.exit(1);
});