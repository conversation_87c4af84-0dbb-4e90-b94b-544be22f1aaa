const mmsiCountryMap = [
    {
        "mid": 201,
        "country_name": "Albania",
        "country_code": "AL"
    },
    {
        "mid": 202,
        "country_name": "Andorra",
        "country_code": "AD"
    },
    {
        "mid": 203,
        "country_name": "Austria",
        "country_code": "AT"
    },
    {
        "mid": 204,
        "country_name": "Portugal",
        "country_code": "PT"
    },
    {
        "mid": 205,
        "country_name": "Belgium",
        "country_code": "BE"
    },
    {
        "mid": 206,
        "country_name": "Belarus",
        "country_code": "BY"
    },
    {
        "mid": 207,
        "country_name": "Bulgaria",
        "country_code": "BG"
    },
    {
        "mid": 208,
        "country_name": "Vatican",
        "country_code": "VA"
    },
    {
        "mid": 209,
        "country_name": "Cyprus",
        "country_code": "CY"
    },
    {
        "mid": 210,
        "country_name": "Cyprus",
        "country_code": "CY"
    },
    {
        "mid": 211,
        "country_name": "Germany",
        "country_code": "DE"
    },
    {
        "mid": 212,
        "country_name": "Cyprus",
        "country_code": "CY"
    },
    {
        "mid": 213,
        "country_name": "Georgia",
        "country_code": "GE"
    },
    {
        "mid": 214,
        "country_name": "Moldova",
        "country_code": "MD"
    },
    {
        "mid": 215,
        "country_name": "Malta",
        "country_code": "MT"
    },
    {
        "mid": 216,
        "country_name": "Armenia",
        "country_code": "AM"
    },
    {
        "mid": 218,
        "country_name": "Germany",
        "country_code": "DE"
    },
    {
        "mid": 219,
        "country_name": "Denmark",
        "country_code": "DK"
    },
    {
        "mid": 220,
        "country_name": "Denmark",
        "country_code": "DK"
    },
    {
        "mid": 224,
        "country_name": "Spain",
        "country_code": "ES"
    },
    {
        "mid": 225,
        "country_name": "Spain",
        "country_code": "ES"
    },
    {
        "mid": 226,
        "country_name": "France",
        "country_code": "FR"
    },
    {
        "mid": 227,
        "country_name": "France",
        "country_code": "FR"
    },
    {
        "mid": 228,
        "country_name": "France",
        "country_code": "FR"
    },
    {
        "mid": 229,
        "country_name": "Malta",
        "country_code": "MT"
    },
    {
        "mid": 230,
        "country_name": "Finland",
        "country_code": "FI"
    },
    {
        "mid": 231,
        "country_name": "Faroe Is",
        "country_code": "FO"
    },
    {
        "mid": 232,
        "country_name": "United Kingdom",
        "country_code": "GB"
    },
    {
        "mid": 233,
        "country_name": "United Kingdom",
        "country_code": "GB"
    },
    {
        "mid": 234,
        "country_name": "United Kingdom",
        "country_code": "GB"
    },
    {
        "mid": 235,
        "country_name": "United Kingdom",
        "country_code": "GB"
    },
    {
        "mid": 236,
        "country_name": "Gibraltar",
        "country_code": "GI"
    },
    {
        "mid": 237,
        "country_name": "Greece",
        "country_code": "GR"
    },
    {
        "mid": 238,
        "country_name": "Croatia",
        "country_code": "HR"
    },
    {
        "mid": 239,
        "country_name": "Greece",
        "country_code": "GR"
    },
    {
        "mid": 240,
        "country_name": "Greece",
        "country_code": "GR"
    },
    {
        "mid": 241,
        "country_name": "Greece",
        "country_code": "GR"
    },
    {
        "mid": 242,
        "country_name": "Morocco",
        "country_code": "MA"
    },
    {
        "mid": 243,
        "country_name": "Hungary",
        "country_code": "HU"
    },
    {
        "mid": 244,
        "country_name": "Netherlands",
        "country_code": "NL"
    },
    {
        "mid": 245,
        "country_name": "Netherlands",
        "country_code": "NL"
    },
    {
        "mid": 246,
        "country_name": "Netherlands",
        "country_code": "NL"
    },
    {
        "mid": 247,
        "country_name": "Italy",
        "country_code": "IT"
    },
    {
        "mid": 248,
        "country_name": "Malta",
        "country_code": "MT"
    },
    {
        "mid": 249,
        "country_name": "Malta",
        "country_code": "MT"
    },
    {
        "mid": 250,
        "country_name": "Ireland",
        "country_code": "IE"
    },
    {
        "mid": 251,
        "country_name": "Iceland",
        "country_code": "IS"
    },
    {
        "mid": 252,
        "country_name": "Liechtenstein",
        "country_code": "LI"
    },
    {
        "mid": 253,
        "country_name": "Luxembourg",
        "country_code": "LU"
    },
    {
        "mid": 254,
        "country_name": "Monaco",
        "country_code": "MC"
    },
    {
        "mid": 255,
        "country_name": "Portugal",
        "country_code": "PT"
    },
    {
        "mid": 256,
        "country_name": "Malta",
        "country_code": "MT"
    },
    {
        "mid": 257,
        "country_name": "Norway",
        "country_code": "NO"
    },
    {
        "mid": 258,
        "country_name": "Norway",
        "country_code": "NO"
    },
    {
        "mid": 259,
        "country_name": "Norway",
        "country_code": "NO"
    },
    {
        "mid": 261,
        "country_name": "Poland",
        "country_code": "PL"
    },
    {
        "mid": 262,
        "country_name": "Montenegro",
        "country_code": "ME"
    },
    {
        "mid": 263,
        "country_name": "Portugal",
        "country_code": "PT"
    },
    {
        "mid": 264,
        "country_name": "Romania",
        "country_code": "RO"
    },
    {
        "mid": 265,
        "country_name": "Sweden",
        "country_code": "SE"
    },
    {
        "mid": 266,
        "country_name": "Sweden",
        "country_code": "SE"
    },
    {
        "mid": 267,
        "country_name": "Slovakia",
        "country_code": "SK"
    },
    {
        "mid": 268,
        "country_name": "San Marino",
        "country_code": "SM"
    },
    {
        "mid": 269,
        "country_name": "Switzerland",
        "country_code": "CH"
    },
    {
        "mid": 270,
        "country_name": "Czech Republic",
        "country_code": "CZ"
    },
    {
        "mid": 271,
        "country_name": "Turkey",
        "country_code": "TR"
    },
    {
        "mid": 272,
        "country_name": "Ukraine",
        "country_code": "UA"
    },
    {
        "mid": 273,
        "country_name": "Russia",
        "country_code": "RU"
    },
    {
        "mid": 274,
        "country_name": "FYR Macedonia",
        "country_code": "MK"
    },
    {
        "mid": 275,
        "country_name": "Latvia",
        "country_code": "LV"
    },
    {
        "mid": 276,
        "country_name": "Estonia",
        "country_code": "EE"
    },
    {
        "mid": 277,
        "country_name": "Lithuania",
        "country_code": "LT"
    },
    {
        "mid": 278,
        "country_name": "Slovenia",
        "country_code": "SI"
    },
    {
        "mid": 279,
        "country_name": "Serbia",
        "country_code": "RS"
    },
    {
        "mid": 301,
        "country_name": "Anguilla",
        "country_code": "AI"
    },
    {
        "mid": 303,
        "country_name": "USA",
        "country_code": "US"
    },
    {
        "mid": 304,
        "country_name": "Antigua Barbuda",
        "country_code": "AG"
    },
    {
        "mid": 305,
        "country_name": "Antigua Barbuda",
        "country_code": "AG"
    },
    {
        "mid": 306,
        "country_name": "Curacao",
        "country_code": "CW"
    },
    {
        "mid": 307,
        "country_name": "Aruba",
        "country_code": "AW"
    },
    {
        "mid": 308,
        "country_name": "Bahamas",
        "country_code": "BS"
    },
    {
        "mid": 309,
        "country_name": "Bahamas",
        "country_code": "BS"
    },
    {
        "mid": 310,
        "country_name": "Bermuda",
        "country_code": "BM"
    },
    {
        "mid": 311,
        "country_name": "Bahamas",
        "country_code": "BS"
    },
    {
        "mid": 312,
        "country_name": "Belize",
        "country_code": "BZ"
    },
    {
        "mid": 314,
        "country_name": "Barbados",
        "country_code": "BB"
    },
    {
        "mid": 316,
        "country_name": "Canada",
        "country_code": "CA"
    },
    {
        "mid": 319,
        "country_name": "Cayman Is",
        "country_code": "KY"
    },
    {
        "mid": 321,
        "country_name": "Costa Rica",
        "country_code": "CR"
    },
    {
        "mid": 323,
        "country_name": "Cuba",
        "country_code": "CU"
    },
    {
        "mid": 325,
        "country_name": "Dominica",
        "country_code": "DM"
    },
    {
        "mid": 327,
        "country_name": "Dominican Rep",
        "country_code": "DO"
    },
    {
        "mid": 329,
        "country_name": "Guadeloupe",
        "country_code": "GP"
    },
    {
        "mid": 330,
        "country_name": "Grenada",
        "country_code": "GD"
    },
    {
        "mid": 331,
        "country_name": "Greenland",
        "country_code": "GL"
    },
    {
        "mid": 332,
        "country_name": "Guatemala",
        "country_code": "GT"
    },
    {
        "mid": 334,
        "country_name": "Honduras",
        "country_code": "HN"
    },
    {
        "mid": 336,
        "country_name": "Haiti",
        "country_code": "HT"
    },
    {
        "mid": 338,
        "country_name": "USA",
        "country_code": "US"
    },
    {
        "mid": 339,
        "country_name": "Jamaica",
        "country_code": "JM"
    },
    {
        "mid": 341,
        "country_name": "St Kitts Nevis",
        "country_code": "KN"
    },
    {
        "mid": 343,
        "country_name": "St Lucia",
        "country_code": "LC"
    },
    {
        "mid": 345,
        "country_name": "Mexico",
        "country_code": "MX"
    },
    {
        "mid": 347,
        "country_name": "Martinique",
        "country_code": "MQ"
    },
    {
        "mid": 348,
        "country_name": "Montserrat",
        "country_code": "MS"
    },
    {
        "mid": 350,
        "country_name": "Nicaragua",
        "country_code": "NI"
    },
    {
        "mid": 351,
        "country_name": "Panama",
        "country_code": "PA"
    },
    {
        "mid": 352,
        "country_name": "Panama",
        "country_code": "PA"
    },
    {
        "mid": 353,
        "country_name": "Panama",
        "country_code": "PA"
    },
    {
        "mid": 354,
        "country_name": "Panama",
        "country_code": "PA"
    },
    {
        "mid": 355,
        "country_name": "Panama",
        "country_code": "PA"
    },
    {
        "mid": 356,
        "country_name": "Panama",
        "country_code": "PA"
    },
    {
        "mid": 357,
        "country_name": "Panama",
        "country_code": "PA"
    },
    {
        "mid": 358,
        "country_name": "Puerto Rico",
        "country_code": "PR"
    },
    {
        "mid": 359,
        "country_name": "El Salvador",
        "country_code": "SV"
    },
    {
        "mid": 361,
        "country_name": "St Pierre Miquelon",
        "country_code": "PM"
    },
    {
        "mid": 362,
        "country_name": "Trinidad Tobago",
        "country_code": "TT"
    },
    {
        "mid": 364,
        "country_name": "Turks Caicos Is",
        "country_code": "TC"
    },
    {
        "mid": 366,
        "country_name": "USA",
        "country_code": "US"
    },
    {
        "mid": 367,
        "country_name": "USA",
        "country_code": "US"
    },
    {
        "mid": 368,
        "country_name": "USA",
        "country_code": "US"
    },
    {
        "mid": 369,
        "country_name": "USA",
        "country_code": "US"
    },
    {
        "mid": 370,
        "country_name": "Panama",
        "country_code": "PA"
    },
    {
        "mid": 371,
        "country_name": "Panama",
        "country_code": "PA"
    },
    {
        "mid": 372,
        "country_name": "Panama",
        "country_code": "PA"
    },
    {
        "mid": 373,
        "country_name": "Panama",
        "country_code": "PA"
    },
    {
        "mid": 374,
        "country_name": "Panama",
        "country_code": "PA"
    },
    {
        "mid": 375,
        "country_name": "St Vincent Grenadines",
        "country_code": "VC"
    },
    {
        "mid": 376,
        "country_name": "St Vincent Grenadines",
        "country_code": "VC"
    },
    {
        "mid": 377,
        "country_name": "St Vincent Grenadines",
        "country_code": "VC"
    },
    {
        "mid": 378,
        "country_name": "British Virgin Is",
        "country_code": "VG"
    },
    {
        "mid": 379,
        "country_name": "US Virgin Is",
        "country_code": "VI"
    },
    {
        "mid": 401,
        "country_name": "Afghanistan",
        "country_code": "AF"
    },
    {
        "mid": 403,
        "country_name": "Saudi Arabia",
        "country_code": "SA"
    },
    {
        "mid": 405,
        "country_name": "Bangladesh",
        "country_code": "BD"
    },
    {
        "mid": 408,
        "country_name": "Bahrain",
        "country_code": "BH"
    },
    {
        "mid": 410,
        "country_name": "Bhutan",
        "country_code": "BT"
    },
    {
        "mid": 412,
        "country_name": "China",
        "country_code": "CN"
    },
    {
        "mid": 413,
        "country_name": "China",
        "country_code": "CN"
    },
    {
        "mid": 414,
        "country_name": "China",
        "country_code": "CN"
    },
    {
        "mid": 416,
        "country_name": "Taiwan",
        "country_code": "TW"
    },
    {
        "mid": 417,
        "country_name": "Sri Lanka",
        "country_code": "LK"
    },
    {
        "mid": 419,
        "country_name": "India",
        "country_code": "IN"
    },
    {
        "mid": 422,
        "country_name": "Iran",
        "country_code": "IR"
    },
    {
        "mid": 423,
        "country_name": "Azerbaijan",
        "country_code": "AZ"
    },
    {
        "mid": 425,
        "country_name": "Iraq",
        "country_code": "IQ"
    },
    {
        "mid": 428,
        "country_name": "Israel",
        "country_code": "IL"
    },
    {
        "mid": 431,
        "country_name": "Japan",
        "country_code": "JP"
    },
    {
        "mid": 432,
        "country_name": "Japan",
        "country_code": "JP"
    },
    {
        "mid": 434,
        "country_name": "Turkmenistan",
        "country_code": "TM"
    },
    {
        "mid": 436,
        "country_name": "Kazakhstan",
        "country_code": "KZ"
    },
    {
        "mid": 437,
        "country_name": "Uzbekistan",
        "country_code": "UZ"
    },
    {
        "mid": 438,
        "country_name": "Jordan",
        "country_code": "JO"
    },
    {
        "mid": 440,
        "country_name": "Korea",
        "country_code": "KR"
    },
    {
        "mid": 441,
        "country_name": "Korea",
        "country_code": "KR"
    },
    {
        "mid": 443,
        "country_name": "Palestine",
        "country_code": "PS"
    },
    {
        "mid": 445,
        "country_name": "DPR Korea",
        "country_code": "KP"
    },
    {
        "mid": 447,
        "country_name": "Kuwait",
        "country_code": "KW"
    },
    {
        "mid": 450,
        "country_name": "Lebanon",
        "country_code": "LB"
    },
    {
        "mid": 451,
        "country_name": "Kyrgyz Republic",
        "country_code": "KG"
    },
    {
        "mid": 453,
        "country_name": "Macao",
        "country_code": "MO"
    },
    {
        "mid": 455,
        "country_name": "Maldives",
        "country_code": "MV"
    },
    {
        "mid": 457,
        "country_name": "Mongolia",
        "country_code": "MN"
    },
    {
        "mid": 459,
        "country_name": "Nepal",
        "country_code": "NP"
    },
    {
        "mid": 461,
        "country_name": "Oman",
        "country_code": "OM"
    },
    {
        "mid": 463,
        "country_name": "Pakistan",
        "country_code": "PK"
    },
    {
        "mid": 466,
        "country_name": "Qatar",
        "country_code": "QA"
    },
    {
        "mid": 468,
        "country_name": "Syria",
        "country_code": "SY"
    },
    {
        "mid": 470,
        "country_name": "UAE",
        "country_code": "AE"
    },
    {
        "mid": 471,
        "country_name": "UAE",
        "country_code": "AE"
    },
    {
        "mid": 472,
        "country_name": "Tajikistan",
        "country_code": "TJ"
    },
    {
        "mid": 473,
        "country_name": "Yemen",
        "country_code": "YE"
    },
    {
        "mid": 475,
        "country_name": "Yemen",
        "country_code": "YE"
    },
    {
        "mid": 477,
        "country_name": "Hong Kong",
        "country_code": "HK"
    },
    {
        "mid": 478,
        "country_name": "Bosnia and Herzegovina",
        "country_code": "BA"
    },
    {
        "mid": 501,
        "country_name": "Antarctica",
        "country_code": "AQ"
    },
    {
        "mid": 503,
        "country_name": "Australia",
        "country_code": "AU"
    },
    {
        "mid": 506,
        "country_name": "Myanmar",
        "country_code": "MM"
    },
    {
        "mid": 508,
        "country_name": "Brunei",
        "country_code": "BN"
    },
    {
        "mid": 510,
        "country_name": "Micronesia",
        "country_code": "FM"
    },
    {
        "mid": 511,
        "country_name": "Palau",
        "country_code": "PW"
    },
    {
        "mid": 512,
        "country_name": "New Zealand",
        "country_code": "NZ"
    },
    {
        "mid": 514,
        "country_name": "Cambodia",
        "country_code": "KH"
    },
    {
        "mid": 515,
        "country_name": "Cambodia",
        "country_code": "KH"
    },
    {
        "mid": 516,
        "country_name": "Christmas Is",
        "country_code": "CX"
    },
    {
        "mid": 518,
        "country_name": "Cook Is",
        "country_code": "CK"
    },
    {
        "mid": 520,
        "country_name": "Fiji",
        "country_code": "FJ"
    },
    {
        "mid": 523,
        "country_name": "Cocos Is",
        "country_code": "CC"
    },
    {
        "mid": 525,
        "country_name": "Indonesia",
        "country_code": "ID"
    },
    {
        "mid": 529,
        "country_name": "Kiribati",
        "country_code": "KI"
    },
    {
        "mid": 531,
        "country_name": "Laos",
        "country_code": "LA"
    },
    {
        "mid": 533,
        "country_name": "Malaysia",
        "country_code": "MY"
    },
    {
        "mid": 536,
        "country_name": "N Mariana Is",
        "country_code": "MP"
    },
    {
        "mid": 538,
        "country_name": "Marshall Is",
        "country_code": "MH"
    },
    {
        "mid": 540,
        "country_name": "New Caledonia",
        "country_code": "NC"
    },
    {
        "mid": 542,
        "country_name": "Niue",
        "country_code": "NU"
    },
    {
        "mid": 544,
        "country_name": "Nauru",
        "country_code": "NR"
    },
    {
        "mid": 546,
        "country_name": "French Polynesia",
        "country_code": "PF"
    },
    {
        "mid": 548,
        "country_name": "Philippines",
        "country_code": "PH"
    },
    {
        "mid": 553,
        "country_name": "Papua New Guinea",
        "country_code": "PG"
    },
    {
        "mid": 555,
        "country_name": "Pitcairn Is",
        "country_code": "PN"
    },
    {
        "mid": 557,
        "country_name": "Solomon Is",
        "country_code": "SB"
    },
    {
        "mid": 559,
        "country_name": "American Samoa",
        "country_code": "AS"
    },
    {
        "mid": 561,
        "country_name": "Samoa",
        "country_code": "WS"
    },
    {
        "mid": 563,
        "country_name": "Singapore",
        "country_code": "SG"
    },
    {
        "mid": 564,
        "country_name": "Singapore",
        "country_code": "SG"
    },
    {
        "mid": 565,
        "country_name": "Singapore",
        "country_code": "SG"
    },
    {
        "mid": 566,
        "country_name": "Singapore",
        "country_code": "SG"
    },
    {
        "mid": 567,
        "country_name": "Thailand",
        "country_code": "TH"
    },
    {
        "mid": 570,
        "country_name": "Tonga",
        "country_code": "TO"
    },
    {
        "mid": 572,
        "country_name": "Tuvalu",
        "country_code": "TV"
    },
    {
        "mid": 574,
        "country_name": "Vietnam",
        "country_code": "VN"
    },
    {
        "mid": 576,
        "country_name": "Vanuatu",
        "country_code": "VU"
    },
    {
        "mid": 577,
        "country_name": "Vanuatu",
        "country_code": "VU"
    },
    {
        "mid": 578,
        "country_name": "Wallis Futuna Is",
        "country_code": "WF"
    },
    {
        "mid": 601,
        "country_name": "South Africa",
        "country_code": "ZA"
    },
    {
        "mid": 603,
        "country_name": "Angola",
        "country_code": "AO"
    },
    {
        "mid": 605,
        "country_name": "Algeria",
        "country_code": "DZ"
    },
    {
        "mid": 607,
        "country_name": "St Paul Amsterdam Is",
        "country_code": "TF"
    },
    {
        "mid": 608,
        "country_name": "Ascension Is",
        "country_code": "IO"
    },
    {
        "mid": 609,
        "country_name": "Burundi",
        "country_code": "BI"
    },
    {
        "mid": 610,
        "country_name": "Benin",
        "country_code": "BJ"
    },
    {
        "mid": 611,
        "country_name": "Botswana",
        "country_code": "BW"
    },
    {
        "mid": 612,
        "country_name": "Cen Afr Rep",
        "country_code": "CF"
    },
    {
        "mid": 613,
        "country_name": "Cameroon",
        "country_code": "CM"
    },
    {
        "mid": 615,
        "country_name": "Congo",
        "country_code": "CG"
    },
    {
        "mid": 616,
        "country_name": "Comoros",
        "country_code": "KM"
    },
    {
        "mid": 617,
        "country_name": "Cape Verde",
        "country_code": "CV"
    },
    {
        "mid": 618,
        "country_name": "Antarctica",
        "country_code": "AQ"
    },
    {
        "mid": 619,
        "country_name": "Ivory Coast",
        "country_code": "CI"
    },
    {
        "mid": 620,
        "country_name": "Comoros",
        "country_code": "KM"
    },
    {
        "mid": 621,
        "country_name": "Djibouti",
        "country_code": "DJ"
    },
    {
        "mid": 622,
        "country_name": "Egypt",
        "country_code": "EG"
    },
    {
        "mid": 624,
        "country_name": "Ethiopia",
        "country_code": "ET"
    },
    {
        "mid": 625,
        "country_name": "Eritrea",
        "country_code": "ER"
    },
    {
        "mid": 626,
        "country_name": "Gabon",
        "country_code": "GA"
    },
    {
        "mid": 627,
        "country_name": "Ghana",
        "country_code": "GH"
    },
    {
        "mid": 629,
        "country_name": "Gambia",
        "country_code": "GM"
    },
    {
        "mid": 630,
        "country_name": "Guinea-Bissau",
        "country_code": "GW"
    },
    {
        "mid": 631,
        "country_name": "Equ. Guinea",
        "country_code": "GQ"
    },
    {
        "mid": 632,
        "country_name": "Guinea",
        "country_code": "GN"
    },
    {
        "mid": 633,
        "country_name": "Burkina Faso",
        "country_code": "BF"
    },
    {
        "mid": 634,
        "country_name": "Kenya",
        "country_code": "KE"
    },
    {
        "mid": 635,
        "country_name": "Antarctica",
        "country_code": "AQ"
    },
    {
        "mid": 636,
        "country_name": "Liberia",
        "country_code": "LR"
    },
    {
        "mid": 637,
        "country_name": "Liberia",
        "country_code": "LR"
    },
    {
        "mid": 642,
        "country_name": "Libya",
        "country_code": "LY"
    },
    {
        "mid": 644,
        "country_name": "Lesotho",
        "country_code": "LS"
    },
    {
        "mid": 645,
        "country_name": "Mauritius",
        "country_code": "MU"
    },
    {
        "mid": 647,
        "country_name": "Madagascar",
        "country_code": "MG"
    },
    {
        "mid": 649,
        "country_name": "Mali",
        "country_code": "ML"
    },
    {
        "mid": 650,
        "country_name": "Mozambique",
        "country_code": "MZ"
    },
    {
        "mid": 654,
        "country_name": "Mauritania",
        "country_code": "MR"
    },
    {
        "mid": 655,
        "country_name": "Malawi",
        "country_code": "MW"
    },
    {
        "mid": 656,
        "country_name": "Niger",
        "country_code": "NE"
    },
    {
        "mid": 657,
        "country_name": "Nigeria",
        "country_code": "NG"
    },
    {
        "mid": 659,
        "country_name": "Namibia",
        "country_code": "NA"
    },
    {
        "mid": 660,
        "country_name": "Reunion",
        "country_code": "RE"
    },
    {
        "mid": 661,
        "country_name": "Rwanda",
        "country_code": "RW"
    },
    {
        "mid": 662,
        "country_name": "Sudan",
        "country_code": "SD"
    },
    {
        "mid": 663,
        "country_name": "Senegal",
        "country_code": "SN"
    },
    {
        "mid": 664,
        "country_name": "Seychelles",
        "country_code": "SC"
    },
    {
        "mid": 665,
        "country_name": "St Helena",
        "country_code": "SH"
    },
    {
        "mid": 666,
        "country_name": "Somalia",
        "country_code": "SO"
    },
    {
        "mid": 667,
        "country_name": "Sierra Leone",
        "country_code": "SL"
    },
    {
        "mid": 668,
        "country_name": "Sao Tome Principe",
        "country_code": "ST"
    },
    {
        "mid": 669,
        "country_name": "Swaziland",
        "country_code": "SZ"
    },
    {
        "mid": 670,
        "country_name": "Chad",
        "country_code": "TD"
    },
    {
        "mid": 671,
        "country_name": "Togo",
        "country_code": "TG"
    },
    {
        "mid": 672,
        "country_name": "Tunisia",
        "country_code": "TN"
    },
    {
        "mid": 674,
        "country_name": "Tanzania",
        "country_code": "TZ"
    },
    {
        "mid": 675,
        "country_name": "Uganda",
        "country_code": "UG"
    },
    {
        "mid": 676,
        "country_name": "DR Congo",
        "country_code": "CD"
    },
    {
        "mid": 677,
        "country_name": "Tanzania",
        "country_code": "TZ"
    },
    {
        "mid": 678,
        "country_name": "Zambia",
        "country_code": "ZM"
    },
    {
        "mid": 679,
        "country_name": "Zimbabwe",
        "country_code": "ZW"
    },
    {
        "mid": 701,
        "country_name": "Argentina",
        "country_code": "AR"
    },
    {
        "mid": 710,
        "country_name": "Brazil",
        "country_code": "BR"
    },
    {
        "mid": 720,
        "country_name": "Bolivia",
        "country_code": "BO"
    },
    {
        "mid": 725,
        "country_name": "Chile",
        "country_code": "CL"
    },
    {
        "mid": 730,
        "country_name": "Colombia",
        "country_code": "CO"
    },
    {
        "mid": 735,
        "country_name": "Ecuador",
        "country_code": "EC"
    },
    {
        "mid": 740,
        "country_name": "UK",
        "country_code": "UK"
    },
    {
        "mid": 745,
        "country_name": "Guiana",
        "country_code": "GF"
    },
    {
        "mid": 750,
        "country_name": "Guyana",
        "country_code": "GY"
    },
    {
        "mid": 755,
        "country_name": "Paraguay",
        "country_code": "PY"
    },
    {
        "mid": 760,
        "country_name": "Peru",
        "country_code": "PE"
    },
    {
        "mid": 765,
        "country_name": "Suriname",
        "country_code": "SR"
    },
    {
        "mid": 770,
        "country_name": "Uruguay",
        "country_code": "UY"
    },
    {
        "mid": 775,
        "country_name": "Venezuela",
        "country_code": "VE"
    }
]

module.exports = mmsiCountryMap;