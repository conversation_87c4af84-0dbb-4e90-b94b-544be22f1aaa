const { SESClient, SendEmailCommand } = require('@aws-sdk/client-ses');

if (!process.env.AWS_SES_REGION) {
    throw new Error("AWS_SES_REGION environment variable is required");
}

if (!process.env.AWS_SES_FROM_EMAIL) {
    throw new Error("AWS_SES_FROM_EMAIL environment variable is required");
}

const sesClient = new SESClient({
    region: process.env.AWS_SES_REGION,
    credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
    }
});

async function sendEmail(to, subject, html) {
    const fromEmail = process.env.AWS_SES_FROM_EMAIL;
    const source = `"${process.env.NODE_ENV === 'local' ? 'TEST ' : ''}Quartermaster System" <${fromEmail}>`;

    const command = new SendEmailCommand({
        Source: source,
        Destination: {
            ToAddresses: [to]
        },
        Message: {
            Subject: {
                Data: subject,
                Charset: 'UTF-8'
            },
            Body: {
                Html: {
                    Data: html,
                    Charset: 'UTF-8'
                }
            }
        }
    });

    try {
        const result = await sesClient.send(command);
        console.log(`[AWS SES] Email sent successfully to ${to}, MessageId: ${result.MessageId}`);
        return result;
    } catch (error) {
        console.error(`[AWS SES] Failed to send email to ${to}:`, error);
        throw error;
    }
}

module.exports = {
    sendEmail
};
