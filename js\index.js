require('dotenv').config();
require('aws-sdk/lib/maintenance_mode_message').suppress = true;

console.log('process has started')

require('./modules/processLogs')
const db = require('./modules/db');
const { postLogToSlack } = require('./modules/notifyLog');

Promise.all(
    Object.values(db).map(db => new Promise((resolve, reject) => {
        db.once('open', resolve);
        db.on('error', reject);
    }))
).then(() => {
    require('./services/iotProcessor')
    // require('./services/statsProcessor')
    // require('./services/notificationAlertsProcessor')
    // require('./services/newArtifactProcessor')
    // require('./services/summaryReportsProcessor')
    // require('./services/vesselOnlineProcessor')
    // require('./services/seaVisionSampleRequests')
    // require('./services/audioProcessor')

    // require('./jobs/optimizeCurrentMonthCoords')
    // require('./services/dbBackup')
})

process.on('uncaughtException', (err) => {
    console.error('(FATAL ERROR) Uncaught Exception:', err)
    postLogToSlack({
        severity: 'fatal',
        message: 'Uncaught Exception in the microservices process',
        stack: err.stack
    })
})

// test node-js ci