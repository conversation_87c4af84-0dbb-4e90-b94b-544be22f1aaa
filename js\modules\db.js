const mongoose = require('mongoose');

mongoose.set('strictQuery', false)

if (!process.env.MONGO_URI) {
    throw new Error("MONGO_URI must be set in env variables");
}

const NODE_ENV = process.env.NODE_ENV;

if (!NODE_ENV) {
    throw new Error("NODE_ENV must be set in env variables");
}

const db = {
    qm: mongoose.createConnection(process.env.MONGO_URI, { dbName: NODE_ENV === 'prod' ? 'quartermaster' : 'quartermaster-local' }),
    qmai: mongoose.createConnection(process.env.MONGO_URI, { dbName: NODE_ENV === 'prod' ? 'artifact_processor' : 'artifact_processor-local' }),
    qmShared: mongoose.createConnection(process.env.MONGO_URI, { dbName: NODE_ENV === 'prod' ? 'quartermaster-shared' : 'quartermaster-shared-local' }),
    aisRaw: mongoose.createConnection(process.env.MONGO_URI, { dbName: NODE_ENV === 'prod' ? 'ais_raw' : 'ais_raw-local' }),
    audio: mongoose.createConnection(process.env.MONGO_URI, { dbName: NODE_ENV === 'prod' ? 'audio_processor' : 'audio_processor-local' }),
    lookups: mongoose.createConnection(process.env.MONGO_URI, { dbName: NODE_ENV === 'prod' ? 'lookups' : 'lookups-local' }),
    locationsOptimized: mongoose.createConnection(process.env.MONGO_URI, { dbName: NODE_ENV === 'prod' ? 'locations_optimized' : 'locations_optimized-local' }),
    locationsRaw: mongoose.createConnection(process.env.MONGO_URI, { dbName: NODE_ENV === 'prod' ? 'locations_raw' : 'locations_raw-local' })
}

db.qm.on('open', () => console.log('DB connected to Quartermaster'))
db.qmai.on('open', () => console.log('DB connected to QMAI'))
db.qmShared.on('open', () => console.log('DB connected to Quartermaster-Shared'));
db.aisRaw.on('open', () => console.log('DB connected to AIS Raw'));
db.audio.on('open', () => console.log('DB connected to Audio'));
db.lookups.on('open', () => console.log('DB connected to Lookups'));
db.locationsOptimized.on('open', () => console.log('DB connected to Location-Optimized'));
db.locationsRaw.on('open', () => console.log('DB connected to Location-Raw'));


db.qm.on('error', (err) => console.error(err))
db.qmai.on('error', (err) => console.error(err))
db.qmShared.on('error', (err) => console.error(err));
db.aisRaw.on('error', (err) => console.error(err));
db.audio.on('error', (err) => console.error(err));
db.lookups.on('error', (err) => console.error(err));
db.locationsOptimized.on('error', (err) => console.error(err));
db.locationsRaw.on('error', (err) => console.error(err));

module.exports = db