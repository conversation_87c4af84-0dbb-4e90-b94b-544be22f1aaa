const { permissions } = require("../utils/permissions");
const Vessel = require('../models/Vessel');

async function getAuthorizedArtifacts(user, notification, artifacts) {
    if (!user || !notification || !Array.isArray(artifacts)) throw new Error('Invalid arguments for getAuthorizedArtifacts');

    if (user.allowed_vessels && !Array.isArray(user.allowed_vessels)) throw new Error('user.allowed_vessels must be an array');
    const userAllowedVessels = user.allowed_vessels.map(id => id.toString());

    if (!user.role.denied_permissions) throw new Error('user.role.denied_permissions must be an array');
    const userHasAccessAllPermission = !user.role.denied_permissions.includes(permissions.accessAllVessels);
    if (!Array.isArray(notification.vessel_ids)) throw new Error('notification.vessel_ids must be an array');
    const notificationVesselIds = notification.vessel_ids.map(id => id.toString());

    const activeVessels = await Vessel.find({ is_active: true }, { _id: 1, is_active: 1 });
    const accessibleVessels = userHasAccessAllPermission
        ? activeVessels
        : activeVessels.filter(v => userAllowedVessels.includes(v._id.toString()));
    const accessibleVesselIds = new Set(accessibleVessels.map(v => v._id.toString()));

    const result = [];

    for (const artifact of artifacts) {
        if (!artifact || !artifact.onboard_vessel_id) continue;
        const artifactVesselId = artifact.onboard_vessel_id.toString();

        if (
            !notificationVesselIds.length ||
            notificationVesselIds.includes(artifactVesselId)
        ) {
            if (accessibleVesselIds.has(artifactVesselId)) {
                result.push(artifact);
            }
        }
    }

    return result;
}

module.exports = {
    getAuthorizedArtifacts
};