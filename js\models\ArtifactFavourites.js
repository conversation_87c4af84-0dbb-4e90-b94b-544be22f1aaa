const mongoose = require("mongoose");
const db = require("../modules/db");
const User = require("./User");

const schema = mongoose.Schema;

const ArtifactFavouritesSchema = new schema(
    {
        user_id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "User",
            required: true,
        },
        artifact_id: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
        },
    },
    {
        timestamps: true, // automatically adds createdAt and updatedAt
    }
);

// No ioEmitter events included

const ArtifactFavourites = db.qm.model(
    "ArtifactFavourites",
    ArtifactFavouritesSchema,
    "artifact_favourites"
);

module.exports = ArtifactFavourites;
