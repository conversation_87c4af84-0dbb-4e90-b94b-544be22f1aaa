const mongoose = require('mongoose');
const db = require('../modules/db');

const statisticsSchema = new mongoose.Schema({
    stats: { type: mongoose.Schema.Types.Mixed, required: true },
    fromTimestamp: { type: Date, required: true, unique: true },
    toTimestamp: { type: Date, required: true, unique: true },
    type: { type: String, required: true, enum: ['weekly', 'daily'] },
}, { minimize: false });

const Statistics = db.qmShared.model('Statistics', statisticsSchema);

module.exports = Statistics