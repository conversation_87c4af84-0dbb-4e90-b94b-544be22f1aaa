// Script to migrate notification alerts: remove "all" from arrays (empty arrays now represent "all")
// Usage: 
//   Dry run: node js/scripts/migrateNotificationAlertsRemoveAll.js --dry-run
//   Execute: node js/scripts/migrateNotificationAlertsRemoveAll.js
require('dotenv').config();
const db = require('../modules/db');
const { getTerminalConfirmation } = require('../utils/functions');

const isDryRun = process.argv.includes('--dry-run');

// Set up stdin for user input
process.stdin.setEncoding('utf8');
process.stdin.resume();

async function migrateNotificationAlerts() {
    try {
        const collection = db.qm.collection('notifications_alerts');
        const alerts = await collection.find({}).toArray();
        let updatedCount = 0;
        let checkedCount = 0;
        
        console.log(`Found ${alerts.length} notification alerts to process.`);
        if (isDryRun) {
            console.log('🔍 DRY RUN MODE - No changes will be made to the database\n');
        }

        const updates = [];

        for (const alert of alerts) {
            let modified = false;
            const updateFields = {};

            // Fields that might contain "all" and need to be updated
            const arrayFields = ['vessel_ids', 'super_category', 'sub_category', 'country_flags', 'title'];

            for (const field of arrayFields) {
                if (alert[field] && Array.isArray(alert[field])) {
                    const hasAll = alert[field].includes('all');
                    
                    if (hasAll) {
                        // Remove "all" from the array
                        updateFields[field] = [];
                        modified = true;
                        const action = isDryRun ? 'Would remove' : 'Removing';
                        console.log(`[${alert._id}] ${action} "all" from ${field}: [${alert[field].join(', ')}] -> []`);
                    }
                }
            }

            checkedCount++;
            if (modified) {
                updates.push({
                    _id: alert._id,
                    updateFields
                });
                updatedCount++;
            }
        }

        console.log(`\n${isDryRun ? 'Would update' : 'Will update'} ${updatedCount} of ${checkedCount} documents.`);

        if (isDryRun) {
            console.log('\n✅ Dry run complete. Run without --dry-run to apply changes.');
            process.exit(0);
        }

        if (updatedCount === 0) {
            console.log('No documents need updating.');
            process.exit(0);
        }

        // Get user confirmation before updating
        const confirmation = await getTerminalConfirmation(
            `\nUpdating ${updatedCount} records in database ${db.qm.name} collection notifications_alerts, continue? (y/n): `
        );

        if (!confirmation) {
            console.log('User did not confirm, exiting...');
            process.exit(0);
        }

        // Perform bulk updates
        console.log('\nApplying bulk updates...');
        const bulkOps = updates.map(update => ({
            updateOne: {
                filter: { _id: update._id },
                update: { $set: update.updateFields }
            }
        }));

        const result = await collection.bulkWrite(bulkOps);
        console.log(`Bulk write result: ${result.modifiedCount} documents modified, ${result.matchedCount} documents matched.`);

        console.log(`\n✅ Migration complete. Updated ${updatedCount} of ${checkedCount} documents.`);
        process.exit(0);
    } catch (err) {
        console.error('Migration failed:', err);
        process.exit(1);
    }
}

Promise.all([
    new Promise((resolve, reject) => {
        db.qm.once('open', resolve);
        db.qm.on('error', reject);
    })
]).then(() => {
    migrateNotificationAlerts()
        .then(() => console.log('Migration completed successfully'))
        .catch(err => console.error('Migration failed:', err));
});

