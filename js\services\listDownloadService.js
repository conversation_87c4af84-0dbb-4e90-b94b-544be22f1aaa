const mongoose = require("mongoose");
const fs = require("fs").promises;
const { createWriteStream, createReadStream } = require("fs");
const path = require("path");
const os = require("os");
const pLimit = require("p-limit");
const axios = require("axios");
const { spawn } = require("child_process");
const { getSignedUrl } = require("@aws-sdk/cloudfront-signer");
const { CloudFrontClient, ListDistributionsCommand } = require("@aws-sdk/client-cloudfront");
const { S3Client } = require("@aws-sdk/client-s3");
const { Upload } = require("@aws-sdk/lib-storage");
const { createLoggerWithPath } = require("../modules/winston");

const db = require("../modules/db");
const List = require("../models/List");
const ListArtifact = require("../models/ListArtifact");
const ArtifactFavourite = require("../models/ArtifactFavourites");
const Vessel = require("../models/Vessel");
const { permissions } = require("../utils/permissions");

const logger = createLoggerWithPath("list_download_service");
const DEST_BUCKET = "quartermaster-list-downloads";
const DEST_REGION = "us-east-1";
const CONCURRENCY = 100;
const CLOUDFRONT_KEY_PAIR_ID = process.env.CLOUDFRONT_KEY_PAIR_ID;
const CLOUDFRONT_PRIVATE_KEY = process.env.CLOUDFRONT_PRIVATE_KEY;

if (!CLOUDFRONT_KEY_PAIR_ID) {
    throw new Error("CLOUDFRONT_KEY_PAIR_ID environment variable is required");
}
if (!CLOUDFRONT_PRIVATE_KEY) {
    throw new Error("CLOUDFRONT_PRIVATE_KEY environment variable is required");
}
const CACHE_REFRESH_INTERVAL = 60 * 60 * 1000;

const cloudFrontClient = new CloudFrontClient({
    region: DEST_REGION,
    credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    },
});

const s3Client = new S3Client({
    region: DEST_REGION,
    credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    },
});

const distributionCache = new Map();
let lastCacheRefresh = 0;

async function refreshDistributionCache() {
    const now = Date.now();
    if (now - lastCacheRefresh < CACHE_REFRESH_INTERVAL && distributionCache.size > 0) return;

    const response = await cloudFrontClient.send(new ListDistributionsCommand({}));
    if (response.DistributionList?.Items) {
        distributionCache.clear();
        for (const dist of response.DistributionList.Items) {
            if (dist.Status === "Deployed" && dist.Origins?.Items) {
                for (const origin of dist.Origins.Items) {
                    if (origin.DomainName && origin.DomainName.includes(".s3.")) {
                        const bucketMatch = origin.DomainName.match(/^([^.]+)\.s3\./);
                        if (bucketMatch) {
                            distributionCache.set(bucketMatch[1], `https://${dist.DomainName}/`);
                        }
                    }
                }
            }
        }
    }
    lastCacheRefresh = now;
    logger.info(`[List Download Service] CloudFront Cache refreshed: ${distributionCache.size} distributions`);
}

function getCloudFrontForBucket(bucketName) {
    return distributionCache.get(bucketName) || null;
}

async function downloadArtifactFromSource(bucket, key, region) {
    const cloudFrontUrl = getCloudFrontForBucket(bucket);
    if (!cloudFrontUrl) {
        throw new Error(`No CloudFront distribution found for bucket: ${bucket}. Available: ${Array.from(distributionCache.keys()).join(", ")}`);
    }

    const fullUrl = `${cloudFrontUrl}${bucket}/${region}/${key}`;
    const signedUrl = getSignedUrl({
        url: fullUrl,
        keyPairId: CLOUDFRONT_KEY_PAIR_ID,
        privateKey: CLOUDFRONT_PRIVATE_KEY,
        dateLessThan: new Date(Date.now() + 3600 * 1000),
    });

    const response = await axios({
        method: "GET",
        url: signedUrl,
        responseType: "stream",
        validateStatus: (status) => status >= 200 && status < 300,
    });
    return response.data;
}

async function downloadArtifactFile(artifact, fileType, localPath, limit) {
    return limit(async () => {
        const key = fileType === "image" ? artifact.image_path : artifact.video_path;
        if (!artifact.bucket_name || !key) {
            logger.warn(`[List Download Service] Missing bucket_name or ${fileType}_path for artifact ${artifact._id}`);
            return { success: false };
        }

        const stream = await downloadArtifactFromSource(artifact.bucket_name, key, artifact.aws_region || DEST_REGION);
        await fs.mkdir(path.dirname(localPath), { recursive: true });

        const writeStream = createWriteStream(localPath);
        await new Promise((resolve, reject) => {
            stream.pipe(writeStream);
            writeStream.on("finish", resolve);
            writeStream.on("error", reject);
            stream.on("error", reject);
        });

        return { success: true };
    });
}

async function saveArtifactJson(artifact, localPath) {
    await fs.writeFile(localPath, JSON.stringify(artifact, null, 2), "utf8");
    return { success: true };
}

async function createAndUploadArchive(downloadPath, s3Key, logger) {
    const tempZipPath = path.join(os.tmpdir(), `archive-${Date.now()}.zip`);

    const files = await fs.readdir(downloadPath);
    if (files.length === 0) {
        throw new Error("No files found in download directory to archive");
    }

    logger.info(`[List Download Service] Creating archive from ${files.length} files`);

    const zipStart = Date.now();
    let zipError = "";
    // const isWindows = process.platform === "win32";

    await new Promise((resolve, reject) => {
        let zipProcess;
        // if (isWindows) {
        //     const psScript = `Compress-Archive -Path "${downloadPath}\\*" -DestinationPath "${tempZipPath}" -Force`;
        //     zipProcess = spawn("powershell", ["-Command", psScript]);
        // } else {
        zipProcess = spawn("zip", ["-0", "-q", "-r", tempZipPath, "."], {
            cwd: path.resolve(downloadPath),
        });
        // }

        zipProcess.stderr.on("data", (data) => {
            zipError += data.toString();
        });

        zipProcess.on("close", (code) => {
            if (code === 0) {
                resolve();
            } else {
                reject(new Error(`Archive creation exited with code ${code}${zipError ? `: ${zipError}` : ""}`));
            }
        });
        zipProcess.on("error", (error) => {
            logger.error(`[List Download Service] Archive process error: ${error.message}`, error);
            reject(error);
        });
    });

    const archiveCreationTime = (Date.now() - zipStart) / 1000;
    logger.info(`[List Download Service] Archive created in ${archiveCreationTime.toFixed(1)}s`);

    const uploadStart = Date.now();
    const upload = new Upload({
        client: s3Client,
        params: {
            Bucket: DEST_BUCKET,
            Key: s3Key,
            Body: createReadStream(tempZipPath),
            ContentType: "application/zip",
        },
        queueSize: 4,
        partSize: 5 * 1024 * 1024,
    });

    await upload.done();
    const archiveUploadTime = (Date.now() - uploadStart) / 1000;
    logger.info(`[List Download Service] Archive uploaded in ${archiveUploadTime.toFixed(1)}s`);

    await fs.unlink(tempZipPath).catch((err) => {
        logger.warn(`[List Download Service] Failed to delete temp zip: ${err.message}`);
    });

    return { archiveCreationTime, archiveUploadTime };
}

function userHasPermissions(user, permissionIds) {
    if (!user.permissions || !Array.isArray(user.permissions)) throw new Error("User permissions is a required array");
    if (!permissionIds || !Array.isArray(permissionIds)) throw new Error("Permission IDs is a required array");
    return permissionIds.every((p_id) => user.permissions.find((p) => p.permission_id === p_id));
}

function canAccessVessel(user, vessel) {
    if (!vessel._id) throw new Error("vessel _id is required");
    if (vessel.is_active === undefined) throw new Error("vessel is_active is required");
    if (!user || !user.permissions || !Array.isArray(user.permissions)) throw new Error("User permissions is a required array");
    if (userHasPermissions(user, [permissions.accessAllVessels])) return true;
    if (!vessel.is_active) return false;
    if (!user.allowed_vessels || !Array.isArray(user.allowed_vessels)) throw new Error("allowed_vessels is a required array");
    return user.allowed_vessels.some((v) => v.toString() === vessel._id.toString());
}

async function getAllowedVesselIds(user) {
    if (!user || !user.permissions || !Array.isArray(user.permissions)) {
        throw new Error("User permissions is a required array");
    }

    if (userHasPermissions(user, [permissions.accessAllVessels])) {
        const allVessels = await Vessel.find({}, { _id: 1, is_active: 1 }).lean();
        return allVessels.filter((v) => canAccessVessel(user, v)).map((v) => v._id);
    }

    if (!user.allowed_vessels || !Array.isArray(user.allowed_vessels)) {
        throw new Error("allowed_vessels is a required array");
    }

    const allowedVesselIds = user.allowed_vessels.map((v) => new mongoose.Types.ObjectId(v));
    const vessels = await Vessel.find({ _id: { $in: allowedVesselIds } }, { _id: 1, is_active: 1 }).lean();
    return vessels.filter((v) => canAccessVessel(user, v)).map((v) => v._id);
}

async function ensureDbConnection() {
    await new Promise((resolve) => {
        if (db.qm.readyState === 1 && db.qmai.readyState === 1) return resolve();
        const check = () => {
            if (db.qm.readyState === 1 && db.qmai.readyState === 1) resolve();
        };
        db.qm.once("open", check);
        db.qmai.once("open", check);
    });
}

async function downloadArtifacts(artifacts, resolvedPath) {
    const limit = pLimit(CONCURRENCY);
    let downloadedCount = 0;
    let downloadErrors = 0;

    const totalFiles = artifacts.reduce((sum, a) => sum + (a.image_path ? 1 : 0) + (a.video_path ? 1 : 0) + 1, 0);
    logger.info(`[List Download Service] Starting download of ${totalFiles} files from ${artifacts.length} artifacts`);

    const downloadPromises = [];
    for (const artifact of artifacts) {
        const artifactId = String(artifact._id);

        downloadPromises.push(
            saveArtifactJson(artifact, path.join(resolvedPath, `${artifactId}.json`)).then((res) => {
                if (res.success) downloadedCount++;
                else downloadErrors++;
            }).catch(() => downloadErrors++)
        );

        if (artifact.image_path) {
            const ext = artifact.image_path.split(".").pop() || "jpg";
            downloadPromises.push(
                downloadArtifactFile(artifact, "image", path.join(resolvedPath, `${artifactId}_image.${ext}`), limit).then((res) => {
                    if (res.success) downloadedCount++;
                    else downloadErrors++;
                }).catch(() => downloadErrors++)
            );
        }
        if (artifact.video_path) {
            const ext = artifact.video_path.split(".").pop() || "mp4";
            downloadPromises.push(
                downloadArtifactFile(artifact, "video", path.join(resolvedPath, `${artifactId}_video.${ext}`), limit).then((res) => {
                    if (res.success) downloadedCount++;
                    else downloadErrors++;
                }).catch(() => downloadErrors++)
            );
        }
    }

    await Promise.all(downloadPromises);

    logger.info(`[List Download Service] Completed: ${downloadedCount} files, ${downloadErrors} errors`);
    if (downloadedCount === 0) {
        throw new Error("No files downloaded");
    }
    return { downloadedCount, downloadErrors };
}

async function downloadListOrFavorites(type, idOrUserId, requestingUserId) {
    const start = Date.now();
    logger.info(`[List Download Service] Starting ${type} download for user ${requestingUserId}, id: ${idOrUserId}`);

    if (type !== "list" && type !== "favorites") {
        throw new Error(`Invalid type: ${type}. Must be "list" or "favorites"`);
    }
    if (!idOrUserId || !requestingUserId) {
        throw new Error("idOrUserId and requestingUserId are required");
    }

    const tempDir = os.tmpdir();
    const downloadPath = path.join(tempDir, `list-download-${Date.now()}-${Math.random().toString(36).substring(7)}`);
    const resolvedPath = path.resolve(downloadPath);

    try {
        await ensureDbConnection();
        logger.info(`[List Download Service] DB connection ensured`);

        const userAggregation = await db.qm.collection("users").aggregate([
            { $match: { _id: new mongoose.Types.ObjectId(requestingUserId) } },
            { $lookup: { from: "roles", localField: "role_id", foreignField: "role_id", as: "role" } },
            { $unwind: { path: "$role", preserveNullAndEmptyArrays: true } },
            {
                $lookup: {
                    from: "permissions",
                    let: { denied_permissions: "$role.denied_permissions" },
                    pipeline: [{ $match: { $expr: { $not: { $in: ["$permission_id", "$$denied_permissions"] } } } }],
                    as: "permissions",
                },
            },
            { $project: { _id: 1, permissions: 1, allowed_vessels: 1, email: 1, name: 1 } },
        ]).toArray();

        if (!userAggregation || userAggregation.length === 0) {
            throw new Error(`Requesting user not found: ${requestingUserId}`);
        }

        const requestingUser = userAggregation[0];
        if (!requestingUser.permissions || !Array.isArray(requestingUser.permissions)) {
            throw new Error(`Requesting user missing or invalid permissions field: ${requestingUserId}`);
        }
        if (!requestingUser.allowed_vessels || !Array.isArray(requestingUser.allowed_vessels)) {
            throw new Error(`Requesting user missing or invalid allowed_vessels field: ${requestingUserId}`);
        }
        if (!requestingUser.email) {
            throw new Error(`Requesting user missing email: ${requestingUserId}`);
        }

        logger.info(`[List Download Service] User permissions validated for ${requestingUser.email}`);
        const allowedVesselIds = await getAllowedVesselIds(requestingUser);
        if (!allowedVesselIds || allowedVesselIds.length === 0) {
            throw new Error("User has no accessible vessels");
        }
        logger.info(`[List Download Service] User has access to ${allowedVesselIds.length} vessels`);

        await fs.mkdir(resolvedPath, { recursive: true });

        let artifacts;
        if (type === "list") {
            const list = await List.findById(idOrUserId);
            if (!list || list.is_deleted) {
                throw new Error(`List not found: ${idOrUserId}`);
            }

            const listArtifacts = await ListArtifact.find({ list_id: new mongoose.Types.ObjectId(idOrUserId) })
                .select("artifact_id")
                .lean();
            const artifactIds = listArtifacts.map((la) => new mongoose.Types.ObjectId(String(la.artifact_id)));
            if (artifactIds.length === 0) {
                throw new Error("List is empty");
            }

            artifacts = await db.qmai.collection("analysis_results").find({
                _id: { $in: artifactIds },
                onboard_vessel_id: { $in: allowedVesselIds },
            }).toArray();
            logger.info(`[List Download Service] Found ${artifacts.length} accessible artifacts in list`);
        } else {
            const favourites = await ArtifactFavourite.find({ user_id: new mongoose.Types.ObjectId(idOrUserId) })
                .select("artifact_id")
                .lean();
            const artifactIds = favourites.map((fav) => new mongoose.Types.ObjectId(String(fav.artifact_id)));
            if (artifactIds.length === 0) {
                throw new Error("No favorite artifacts found");
            }

            artifacts = await db.qmai.collection("analysis_results").find({
                _id: { $in: artifactIds },
                onboard_vessel_id: { $in: allowedVesselIds },
            }).toArray();
            logger.info(`[List Download Service] Found ${artifacts.length} accessible favorite artifacts`);
        }

        if (artifacts.length === 0) {
            throw new Error(`No accessible artifacts found in ${type}`);
        }

        await refreshDistributionCache();
        const { downloadedCount, downloadErrors } = await downloadArtifacts(artifacts, resolvedPath);

        if (downloadedCount === 0) {
            throw new Error("No files were successfully downloaded");
        }

        const prefix = type === "list" ? "list" : "favorites";
        const archiveFileName = `${prefix}-${idOrUserId}-${Date.now()}.zip`;
        const archiveKey = `list-downloads/${prefix}-${idOrUserId}-${Date.now()}/${archiveFileName}`;

        logger.info(`[List Download Service] Creating archive: ${archiveKey}`);
        const timings = await createAndUploadArchive(resolvedPath, archiveKey, logger);

        await fs.rm(resolvedPath, { recursive: true, force: true }).catch((err) => {
            logger.warn(`[List Download Service] Failed to delete download directory: ${err.message}`);
        });

        await refreshDistributionCache();
        const cloudFrontUrl = getCloudFrontForBucket(DEST_BUCKET);
        if (!cloudFrontUrl) {
            throw new Error(`No CloudFront distribution found for bucket: ${DEST_BUCKET}`);
        }

        const fullUrl = `${cloudFrontUrl}${archiveKey}`;
        const signedUrl = getSignedUrl({
            url: fullUrl,
            keyPairId: CLOUDFRONT_KEY_PAIR_ID,
            privateKey: CLOUDFRONT_PRIVATE_KEY,
            dateLessThan: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        });

        const totalTime = (Date.now() - start) / 1000;
        logger.info(`[List Download Service] Completed in ${totalTime.toFixed(1)}s: ${downloadedCount} files, ${downloadErrors} errors`);

        return {
            archiveKey,
            signedUrl,
            downloadedCount,
            downloadErrors,
            timings: { ...timings, totalTime },
            userEmail: requestingUser.email,
            userName: requestingUser.name,
        };
    } catch (error) {
        await fs.rm(resolvedPath, { recursive: true, force: true }).catch((err) => {
            logger.warn(`[List Download Service] Failed to cleanup on error: ${err.message}`);
        });
        throw error;
    }
}

module.exports = {
    downloadListOrFavorites,
};
